# Generated by Django 4.2.23 on 2025-08-13 11:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('product_feeds', '0044_alter_feedcategorycopy_color'),
    ]

    operations = [
        migrations.AlterField(
            model_name='feedcategorycopy',
            name='color',
            field=models.IntegerField(choices=[(0, 'T1 - White'), (1, 'T1 - Black'), (3, 'T1 - Grey'), (7, 'T1 - <PERSON>'), (8, 'T1 - <PERSON>'), (9, 'T1 - <PERSON>'), (11, 'T1 - <PERSON>'), (100, 'T2 - White'), (101, 'T2 - Terracotta'), (102, 'T2 - Midnight Blue'), (103, 'T2 - Sand'), (106, 'T2 - <PERSON><PERSON>'), (107, 'T2 - <PERSON>'), (108, 'T2 - <PERSON>'), (109, 'T2 - <PERSON>'), (110, 'T2 - <PERSON>'), (115, 'T2 - <PERSON><PERSON><PERSON>'), (116, 'T2 - <PERSON>'), (117, 'T2 - <PERSON>'), (119, 'T2 - Black'), (200, 'T1V - <PERSON>'), (201, 'T1V - <PERSON>'), (202, 'T1V - <PERSON> Oak'), (300, 'T3 - <PERSON>'), (301, '<PERSON>3 - <PERSON><PERSON>'), (302, 'T3 - <PERSON>rap<PERSON><PERSON>'), (303, 'T3 - <PERSON><PERSON> <PERSON>'), (304, 'T3 - <PERSON> <PERSON>'), (306, '<PERSON>3 - <PERSON><PERSON><PERSON><PERSON> <PERSON>'), (312, 'T3 - <PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON>'), (313, 'T3 - <PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON>'), (314, 'T3 - <PERSON><PERSON><PERSON><PERSON> <PERSON> <PERSON>'), (315, 'T3 - White Stone Gray'), (316, 'T3 - White Sage Green'), (317, 'T3 - White Misty Blue'), (318, 'T3 - Cashmere Stone Gray'), (319, 'T3 - Cashmere Sage Green'), (320, 'T3 - Cashmere Misty Blue'), (400, 'T13 - White'), (403, 'T13 - Gray'), (405, 'T13 - White Plywood'), (406, 'T13 - Gray Plywood'), (408, 'T13 - Clay Brown'), (409, 'T13 - Olive Green'), (410, 'T13 - Beige'), (411, 'T13 - Black')], null=True),
        ),
    ]
