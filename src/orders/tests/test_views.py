from datetime import timed<PERSON><PERSON>
from decimal import Decimal
from gettext import gettext
from unittest import mock
from unittest.mock import patch
from urllib.parse import urljoin

from django.urls import (
    reverse,
    translate_url,
)
from rest_framework import status

import factory
import pytest
import requests_mock

from custom.constants import (
    VAT_EU,
    VAT_NORMAL,
)
from custom.enums import LanguageEnum
from custom.models import (
    Countries,
    GlobalSettings,
)
from customer_service.enums import CSCorrectionRequestStatus
from gallery.tests.factories import JettyFactory
from invoice.choices import InvoiceStatus
from orders.enums import OrderStatus
from orders.services.status_description import get_status_description
from pricing_v3.services.price_calculators import OrderPriceCalculator
from producers.choices import ProductStatus


@pytest.fixture
def generic_jetty_data():
    generic_jetty_data = factory.build(dict, FACTORY_CLASS=JettyFactory)
    # User is not JSON serializable
    generic_jetty_data.pop('owner', None)
    return generic_jetty_data


@pytest.fixture
def generic_order_data(generic_jetty_data):
    return {
        'country': 'germany',
        'items': [
            {
                'price': 123,
                'price_net': 100,
                'order_item': {
                    'type': 'jetty',
                    **generic_jetty_data,
                },
            }
        ],
    }


@pytest.fixture
def vat_data():
    return {
        'vat': 'DE262231084',
        'invoice_vat': 'DE262231084',
        'vat_type': VAT_EU,
        'company_name': 'sample_company',
        'invoice_company_name': 'sample_company',
    }


@pytest.fixture
def incorrect_vat_data():
    return {
        'vat': 'incorrect',
        'invoice_vat': 'incorrect',
    }


@pytest.fixture
def b2b_order(order_factory, vat_data):
    order = order_factory(**vat_data, country='germany')
    return order


@pytest.fixture
def voucher(voucher_factory):
    voucher = voucher_factory(is_absolute=True)
    return voucher


@pytest.mark.django_db
class TestOrderViews:
    def test_order_change_status_to_draft(self, api_client, user, order_factory):
        order = order_factory(owner=user, status=OrderStatus.CART)
        api_client.force_authenticate(user)
        url = reverse('order-change-status-to-draft', kwargs={'pk': order.id})
        response = api_client.post(url)
        assert response.status_code == status.HTTP_200_OK

        order.refresh_from_db()
        assert order.status == OrderStatus.DRAFT

    def test_order_post_method_not_allowed(self, api_client, user, generic_order_data):
        api_client.force_authenticate(user)
        response = api_client.post(
            reverse('order-list'),
            generic_order_data,
            format='json',
        )
        assert response.status_code == status.HTTP_405_METHOD_NOT_ALLOWED

    def test_change_order_total_price_after_setting_vat_number(
        self,
        api_client,
        vat_data,
        order_factory,
    ):
        order = order_factory(country='germany')
        OrderPriceCalculator(order).calculate()

        vat_amount = order.get_vat_value()
        total_price_with_normal_vat = order.get_total_value()

        url = reverse('order-detail', args=[order.id])
        api_client.force_authenticate(order.owner)
        response = api_client.put(url, vat_data)
        assert response.status_code == status.HTTP_200_OK

        order.refresh_from_db()
        total_price_b2b = order.get_total_value()
        assert order.vat_type == VAT_EU
        assert (
            pytest.approx(total_price_b2b + vat_amount, abs=0.1)
            == total_price_with_normal_vat
        )

    def test_change_order_with_promo_code_total_price_after_setting_vat_number(
        self,
        api_client,
        vat_data,
        voucher,
        order_factory,
    ):
        order = order_factory(country='germany', vouchers=[voucher])
        OrderPriceCalculator(order).calculate(check_vat=True)
        total_price = order.get_total_value()
        vat_amount = order.get_vat_value()
        current_rate = order.region.currency.current_rate.rate
        promo_amount = (Decimal(voucher.value) * current_rate).quantize(Decimal('01'))
        promo_amount_net = order.region_promo_amount_net
        assert order.region_promo_amount == promo_amount

        url = reverse('order-detail', args=[order.id])
        api_client.force_authenticate(order.owner)
        response = api_client.put(url, vat_data)
        assert response.status_code == status.HTTP_200_OK

        order.refresh_from_db()
        assert order.vat_type == VAT_EU
        assert order.region_promo_amount == promo_amount

        total_price_b2b = order.get_total_value()
        promo_diff_after_vat_changing = promo_amount - promo_amount_net
        assert (
            pytest.approx(total_price, abs=0.1)
            == total_price_b2b + vat_amount + promo_diff_after_vat_changing
        )

    def test_update_b2b_order_with_incorrect_vat_number(
        self,
        api_client,
        incorrect_vat_data,
        b2b_order,
    ):
        OrderPriceCalculator(b2b_order).calculate()
        total_price_with_correct_vat = b2b_order.get_total_value()
        vat_amount = b2b_order.get_vat_value()
        assert vat_amount == 0

        url = reverse('order-detail', args=[b2b_order.id])
        api_client.force_authenticate(b2b_order.owner)
        response = api_client.put(url, data=incorrect_vat_data, format='json')
        assert response.status_code == status.HTTP_200_OK

        b2b_order.refresh_from_db()
        total_price_with_incorrect_vat = b2b_order.get_total_value()
        vat_amount = b2b_order.get_vat_value()
        assert b2b_order.vat_type == VAT_NORMAL
        assert (
            pytest.approx(total_price_with_incorrect_vat, abs=0.1)
            == total_price_with_correct_vat + vat_amount
        )

    def test_update_b2b_order_with_promo_code_and_incorrect_vat_number(
        self,
        api_client,
        incorrect_vat_data,
        b2b_order,
        voucher,
    ):
        b2b_order.vouchers.add(voucher)
        OrderPriceCalculator(b2b_order).calculate()

        total_price_with_correct_vat = b2b_order.get_total_value()
        current_rate = b2b_order.region.currency.current_rate.rate
        promo_amount = (Decimal(voucher.value) * current_rate).quantize(Decimal('01'))
        vat_amount = b2b_order.get_vat_value()
        assert b2b_order.region_promo_amount == promo_amount
        assert vat_amount == 0

        url = reverse('order-detail', args=[b2b_order.id])
        api_client.force_authenticate(b2b_order.owner)
        response = api_client.put(url, data=incorrect_vat_data, format='json')
        assert response.status_code == status.HTTP_200_OK

        b2b_order.refresh_from_db()
        total_price_with_incorrect_vat = b2b_order.get_total_value()
        vat_amount = b2b_order.get_vat_value()
        promo_amount_net = b2b_order.region_promo_amount_net
        assert b2b_order.region_promo_amount == promo_amount
        assert voucher in b2b_order.vouchers.all()
        assert b2b_order.vat_type == VAT_NORMAL

        promo_diff_after_vat_change = promo_amount - promo_amount_net
        assert (
            pytest.approx(total_price_with_incorrect_vat, abs=0.1)
            == total_price_with_correct_vat + vat_amount + promo_diff_after_vat_change
        )

    def test_preview_gtm_purchase_event_returns_response_from_internal_request(
        self,
        order,
        api_client,
    ):
        url = reverse('order-preview-gtm-purchase-event', args=[order.id])

        with requests_mock.Mocker() as mock_request:
            mock_request.register_uri(
                'POST',
                'https://atupale.tylko.com/f_purchase',
                request_headers={'X-Gtm-Server-Preview': 'test header'},
                status_code=status.HTTP_200_OK,
                text='response text',
            )

            api_client.force_authenticate(order.owner)
            response = api_client.post(url, data={'preview_header': 'test header'})
            assert response.status_code == 200
            assert response.data == 'response text'


@pytest.mark.django_db
class TestOrderStatus:
    order_status_url = reverse('order_status-list')

    def test_items(self, order_factory, api_client, user):
        order = order_factory(status=OrderStatus.PAYMENT_PENDING, items=[])
        data = {
            'order': 'RV/00237/10/2019/{}/DE'.format(order.id),
            'email': order.email,
            'postal_code': order.postal_code,
        }
        api_client.force_authenticate(user)
        response = api_client.get(self.order_status_url, data=data, format='json')
        assert response.status_code == status.HTTP_200_OK
        assert len(response.data['items']) == order.items.count()
        assert all(
            [  # noqa: C419
                item['status'] == OrderStatus.PAYMENT_PENDING
                for item in response.data['items']
            ]
        )

    def test_description_returns_generic_error_if_incorrect_data(
        self,
        order,
        api_client,
        user,
    ):
        data = {
            'order': 'RV/00237/10/2019/{}/DE'.format(order.id),
            'email': '<EMAIL>',
        }
        expected_status = get_status_description(
            'no_order',
            None,
            empty_lines=False,
        )
        api_client.force_authenticate(user)
        response = api_client.get(self.order_status_url, data=data, format='json')
        assert response.status_code == status.HTTP_200_OK
        assert response.data['status'] == expected_status

    def test_with_postal_code(
        self,
        order_factory,
        order_item_factory,
        product_factory,
        api_client,
        user,
    ):
        test_postal_code = '12-345'
        order = order_factory(
            postal_code=test_postal_code,
            items=[],
            status=OrderStatus.IN_PRODUCTION,
        )
        order_item = order_item_factory(order=order)
        product_factory(
            order_item=order_item,
            order=order,
            status=ProductStatus.NEW,
        )

        data = {
            'order': 'R/454504/01{}/DE'.format(order.id),
            'postal_code': test_postal_code,
        }

        api_client.force_authenticate(user)
        response = api_client.get(self.order_status_url, data=data, format='json')

        assert response.status_code, status.HTTP_200_OK
        assert response.data['items'][0]['status'] == OrderStatus.IN_PRODUCTION.value

    def test_with_postal_code_and_additional_service(
        self,
        order_factory,
        order_item_factory,
        product_factory,
        api_client,
        user,
    ):
        test_postal_code = '12-345'
        order = order_factory(
            postal_code=test_postal_code,
            items=[],
            status=OrderStatus.IN_PRODUCTION,
        )
        order_item = order_item_factory(order=order)
        order_item_factory(order=order, is_old_sofa_collection=True)
        product_factory(
            order_item=order_item,
            order=order,
            status=ProductStatus.NEW,
        )

        data = {
            'order': 'R/454504/01{}/DE'.format(order.id),
            'postal_code': test_postal_code,
        }

        api_client.force_authenticate(user)
        response = api_client.get(self.order_status_url, data=data, format='json')

        assert response.status_code, status.HTTP_200_OK
        assert response.data['items'][0]['status'] == OrderStatus.IN_PRODUCTION.value

    @pytest.mark.parametrize('postal_code', ['12-345', 'AT B345', 'at b345'])
    def test_with_invoice_postal_code(
        self,
        postal_code,
        order_factory,
        order_item_factory,
        product_factory,
        api_client,
        user,
    ):
        order = order_factory(
            invoice_postal_code=postal_code,
            items=[],
            status=OrderStatus.IN_PRODUCTION,
        )
        order_item = order_item_factory(order=order)
        product_factory(
            order_item=order_item,
            order=order,
            status=ProductStatus.NEW,
        )
        data = {
            'order': 'R/454504/01{}/DE'.format(order.id),
            'postal_code': postal_code,
        }

        api_client.force_authenticate(user)
        response = api_client.get(self.order_status_url, data=data, format='json')

        assert response.status_code == status.HTTP_200_OK
        assert response.data['items'][0]['status'] == OrderStatus.IN_PRODUCTION.value


@pytest.mark.django_db
class TestPaymentLink:
    @pytest.mark.parametrize(
        'payment_url',
        ('order-payment-url', 'order-payment-url-live'),  # noqa: PT007
    )
    def test_get_payment_link(
        self,
        order_factory,
        api_client,
        user,
        payment_url,
    ):
        order = order_factory(
            owner=user,
            region=user.profile.region,
            status=OrderStatus.CART,
            region__germany=False,
        )
        api_client.force_authenticate(user)
        GlobalSettings.set_payment_sandbox()

        response = api_client.get(
            reverse(payment_url, args=[order.id]),
            format='json',
        )
        assert response.status_code == status.HTTP_200_OK

        adyen_url = response.data['redirect_url']
        assert f'shopperReference=client-{user.id}' in adyen_url
        assert f'currencyCode={order.region.currency.code}' in adyen_url

    def test_add_order_and_get_payment_link_on_live(
        self,
        order_factory,
        api_client,
        user,
        settings,
    ):
        order = order_factory(
            owner=user,
            status=OrderStatus.CART,
            region__germany=False,
        )
        api_client.force_authenticate(user)

        GlobalSettings.set_payment_live()
        settings.ADYEN_LIVE = {
            'ENVIRONMENT': 'live',
            'DEFAULT_SKIN': 'jkLuKwmh',
            'MERCHANT_ACCOUNT': 'CSTM',
            'ADYEN_HMAC_KEY': '7ddbccbba45b12db93bb3fc870733e18e0bdb86c',
        }

        response = api_client.get(
            reverse('order-payment-url-live', args=[order.id]),
            format='json',
        )
        assert response.status_code == status.HTTP_200_OK

        adyen_url = response.data['redirect_url']
        assert 'merchantAccount=CSTM' in adyen_url
        assert 'skinCode=jkLuKwmh' in adyen_url
        assert f'currencyCode={order.region.currency.code}' in adyen_url


@pytest.mark.django_db
class TestMailingOrderSummary:
    @pytest.mark.parametrize('language', LanguageEnum.values)
    @patch('gallery.models.Jetty.get_max_load', return_value=1)
    @patch('gallery.models.Jetty.get_compartment_max_load', return_value=1)
    def test_expected_data_response(
        self,
        _,  # noqa: PT019
        __,  # noqa: PT019
        order_factory,
        settings,
        api_client,
        language,
        country_factory,
        euro_currency,
    ):
        country = country_factory(
            code=Countries.get_countries_by_language_code(language)[0].code,
            region__currency=euro_currency,
        )
        order = order_factory(
            region=country.region,
            status=OrderStatus.CART,
            owner__profile__language=language,
            owner__profile__region=country.region,
        )
        order_item_one, order_item_two = order.items.order_by('id')

        expected_order_status_url = translate_url(
            urljoin(
                settings.SITE_URL,
                (
                    f'{language}-{country.code.lower()}/contact/?topic=order_status'
                    f'&order_id={order.id}&postal_code={order.postal_code}'
                ),
            ),
            language,
        )

        url = reverse('mailing_order_summary-detail', args=(order.id,))
        api_client.force_authenticate(order.owner)
        headers = {'HTTP_BRAZE_APP_ID': 'app_id', 'HTTP_BRAZE_API_KEY': 'api_key'}
        response = api_client.get(url, **headers)

        response_data = response.json()
        assert response.status_code == status.HTTP_200_OK
        assert response_data['items'] == [
            {
                'image_url': order_item_one.order_item.preview.url or '',
                'rectangular_image_url': order_item_one.order_item.preview.url or '',
                'item_url': order_item_one.order_item.get_url_with_region(order.region),
                'description': order_item_one.order_item.get_item_description(),
                'height': order_item_one.order_item.height,
                'width': order_item_one.order_item.width,
                'depth': order_item_one.order_item.depth,
                'wooden_material': (
                    order_item_one.order_item.get_material()
                    or gettext('particleboard_t13')
                ),
                'max_load': 1,
                'category': (
                    order_item_one.order_item.furniture_category.translated_name
                ),
                'color': order_item_one.order_item.color.translated_color,
                'compartment_max_load': 1,
                'furniture_type': 'jetty',
                'region_price_display': (
                    order.display_regionalized(order_item_one.region_price)
                ),
                'assembly_manual_link': None,
                'disassembly_manual_link': (
                    LanguageEnum(language).get_disassembly_manual_link(
                        order_item_one.order_item.height
                    )
                ),
                'product_id': None,
            },
            {
                'image_url': order_item_two.order_item.preview.url or '',
                'item_url': (
                    order_item_two.order_item.get_url_with_region(order.region)
                ),
                'rectangular_image_url': order_item_two.order_item.preview.url or '',
                'description': order_item_two.order_item.get_item_description(),
                'height': order_item_two.order_item.height,
                'width': order_item_two.order_item.width,
                'depth': order_item_two.order_item.depth,
                'wooden_material': (
                    order_item_two.order_item.get_material()
                    or gettext('particleboard_t13')
                ),
                'max_load': 1,
                'category': (
                    order_item_two.order_item.furniture_category.translated_name
                ),
                'color': order_item_two.order_item.color.translated_color,
                'compartment_max_load': 1,
                'furniture_type': 'jetty',
                'region_price_display': (
                    order.display_regionalized(order_item_two.region_price)
                ),
                'assembly_manual_link': None,
                'disassembly_manual_link': (
                    LanguageEnum(language).get_disassembly_manual_link(
                        order_item_two.order_item.height
                    )
                ),
                'product_id': None,
            },
        ]
        assert response_data['items_count'] == 2
        assert response_data['total_price_display'] == order.get_total_price()
        assert response_data['pricing']['total_price'] == order.get_total_price_number()
        assert response_data['pricing']['total_price_before_discount'] == (
            float(order.get_total_price_number_before_discount())
        )
        assert response_data['pricing']['assembly_price'] == (
            float(order.get_assembly_price())
        )
        assert response_data['pricing']['discount_value'] == (
            float(order.region_promo_amount)
        )
        assert response_data['pricing']['recycle_tax_value'] == (
            float(order.get_recycle_tax_value)
        )
        assert response_data['currency'] == order.currency
        assert response_data['first_name'] == order.first_name
        assert response_data['last_name'] == order.last_name
        assert response_data['street_address_1'] == order.street_address_1
        assert response_data['street_address_2'] == order.street_address_2
        assert response_data['company_name'] == order.company_name
        assert response_data['city'] == order.city
        assert response_data['postal_code'] == order.postal_code
        assert response_data['country'] == order.country
        assert response_data['country_area'] == order.country_area
        assert response_data['phone'] == order.phone
        assert response_data['invoice_for_company'] == order.invoice_for_company
        assert response_data['invoice_company_name'] == order.invoice_company_name
        assert response_data['invoice_first_name'] == order.invoice_first_name
        assert response_data['invoice_last_name'] == order.invoice_last_name
        assert response_data['invoice_email'] == order.invoice_email
        assert response_data['invoice_vat'] == order.invoice_vat
        assert response_data['invoice_street_address_1'] == (
            order.invoice_street_address_1
        )
        assert response_data['invoice_street_address_2'] == (
            order.invoice_street_address_2
        )
        assert response_data['invoice_city'] == order.invoice_city
        assert response_data['invoice_postal_code'] == order.invoice_postal_code
        assert response_data['invoice_country'] == order.invoice_country
        assert response_data['invoice_country_area'] == order.invoice_country_area
        assert response_data['chosen_payment_method'] == order.chosen_payment_method
        assert (
            response_data['estimated_production_date']
            == (order.production_range_formatted.split(' - ')[-1])
        )
        assert response_data['is_order_delayed'] is False
        assert response_data['estimated_production_year'] == (
            int(order.production_range_formatted.split(' - ')[-1][-4:])
        )
        assert response_data['estimated_production_date_range'] == (
            order.production_range_formatted
        )
        assert (
            response_data['estimated_delivery_date']
            == (order.delivery_range_formatted.split(' - ')[-1])
        )
        assert response_data['estimated_delivery_date_range'] == (
            order.delivery_range_formatted
        )
        assert response_data['order_status_url'] == expected_order_status_url

    @patch('gallery.models.Jetty.get_max_load', return_value=1)
    @patch('gallery.models.Jetty.get_compartment_max_load', return_value=1)
    def test_delay_production_date(
        self,
        _,  # noqa: PT019
        __,  # noqa: PT019
        order_factory,
        api_client,
        euro_currency,
        country_factory,
    ):
        country = country_factory(
            region__currency=euro_currency,
        )
        order = order_factory(
            region=country.region,
            status=OrderStatus.CART,
            owner__profile__region=country.region,
        )
        delayed_date = order.estimated_delivery_time + timedelta(weeks=3)
        order.delayed_production_end_time = delayed_date
        order.save()

        url = reverse('mailing_order_summary-detail', args=(order.id,))
        api_client.force_authenticate(order.owner)
        headers = {'HTTP_BRAZE_APP_ID': 'app_id', 'HTTP_BRAZE_API_KEY': 'api_key'}
        response = api_client.get(url, **headers)

        response_data = response.json()
        assert response.status_code == status.HTTP_200_OK
        assert (
            response_data['estimated_production_date']
            == (order.production_range_formatted.split(' - ')[-1])
        )
        assert response_data['is_order_delayed'] is True
        assert response_data['estimated_production_year'] == (
            int(order.production_range_formatted.split(' - ')[-1][-4:])
        )
        assert response_data['estimated_production_date_range'] == (
            order.production_range_formatted
        )
        assert (
            response_data['estimated_delivery_date']
            == (order.delivery_range_formatted.split(' - ')[-1])
        )
        assert response_data['estimated_delivery_date_range'] == (
            order.delivery_range_formatted
        )


@pytest.mark.django_db
class TestAbortOrderView:
    def test_post_should_return_error_when_pending_correction_request(
        self,
        user_factory,
        order_factory,
        product_factory,
        invoice_factory,
        cs_correction_request_factory,
        admin_user,
        api_client,
    ):
        order = order_factory(status=OrderStatus.IN_PRODUCTION)
        product = product_factory(order=order, status=ProductStatus.NEW)
        invoice = invoice_factory(
            order=order,
            status=InvoiceStatus.ENABLED,
            pretty_id=f'0001/03/2024/{order.id}/DE',
        )
        cs_correction_request_factory(
            correction_amount_gross=Decimal('10.00'),
            invoice=invoice,
            issuer=admin_user,
            status=CSCorrectionRequestStatus.STATUS_NEW,
        )

        customer_service_user = user_factory(is_customer_service=True)
        api_client.force_login(customer_service_user)

        url = reverse('abort_order', args=[product.pk])

        response = api_client.post(url, {}, format='json')
        assert response.status_code == status.HTTP_302_FOUND

    @mock.patch('customer_service.views.request_order_abort')
    def test_post_should_call_request_order_abort_when_no_pending_correction_request(
        self,
        mocked_request_order_abort,
        user_factory,
        order_factory,
        product_factory,
        invoice_factory,
        admin_user,
        api_client,
    ):
        order = order_factory(status=OrderStatus.IN_PRODUCTION)
        product = product_factory(order=order, status=ProductStatus.NEW)
        invoice_factory(
            order=order,
            status=InvoiceStatus.ENABLED,
            pretty_id=f'0001/03/2024/{order.id}/DE',
        )

        customer_service_user = user_factory(is_customer_service=True)
        api_client.force_login(customer_service_user)

        url = reverse('abort_order', args=[product.pk])

        response = api_client.post(url, {}, format='json')
        assert response.status_code == status.HTTP_302_FOUND
        assert mocked_request_order_abort.called_once_with(
            order=order,
            order_items_with_quantity=[
                {
                    'order_item_id': product.order_item.id,
                    'quantity': 1,
                }
            ],
            user=customer_service_user,
        )
