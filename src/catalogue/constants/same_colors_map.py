"""Maps color variants.

Used for preventing color repetition within color groups.
"""

from catalogue.constants.color_groupings import (
    BLA<PERSON><PERSON>_COLORS,
    DARK_GREY_COLORS,
    GREEN_COLORS,
    MUSTARD_YELLOW_COLORS,
    RED_AND_PINK_COLORS,
    SAND_COLORS,
    STRICT_BEIGE_COLORS,
    STRICT_GREY_COLORS,
    STRICT_WHITE_COLORS,
    WOODEN_COLORS,
)
from custom.enums import (
    ShelfType,
    Sofa01Color,
    Type01Color,
    Type02Color,
    Type03Color,
    Type13Color,
    Type23Color,
    Type24Color,
    Type25Color,
    VeneerType01Color,
    VeneerType13Color,
)

SAME_COLORS_MAP = {
    ShelfType.TYPE01: {
        Type01Color.WHITE: STRICT_WHITE_COLORS,
        Type01Color.BLACK: BLACK_COLORS,
        Type01Color.GREY: STRICT_GREY_COLORS,
        Type01Color.RED: {(ShelfType.TYPE01, Type01Color.RED)},
        Type01Color.YELLOW: {(ShelfType.TYPE01, Type01Color.YELLOW)},
        Type01Color.DUSTY_PINK: {(ShelfType.TYPE01, Type01Color.DUSTY_PINK)},
        Type01Color.BLUE: {(ShelfType.TYPE01, Type01Color.BLUE)},
        Type01Color.DARK_BROWN: {(ShelfType.TYPE01, Type01Color.DARK_BROWN)},
        Type01Color.MOSS_GREEN: {(ShelfType.TYPE01, Type01Color.MOSS_GREEN)},
    },
    ShelfType.VENEER_TYPE01: {
        VeneerType01Color.ASH: WOODEN_COLORS,
        VeneerType01Color.OAK: WOODEN_COLORS,
        VeneerType01Color.DARK_OAK: WOODEN_COLORS,
    },
    ShelfType.TYPE02: {
        Type02Color.WHITE: STRICT_WHITE_COLORS,
        Type02Color.TERRACOTTA: {(ShelfType.TYPE02, Type02Color.TERRACOTTA)},
        Type02Color.MIDNIGHT_BLUE: {(ShelfType.TYPE02, Type02Color.MIDNIGHT_BLUE)},
        Type02Color.SAND: SAND_COLORS,
        Type02Color.MATTE_BLACK: BLACK_COLORS,
        Type02Color.SKY_BLUE: {(ShelfType.TYPE02, Type02Color.SKY_BLUE)},
        Type02Color.BURGUNDY: {(ShelfType.TYPE02, Type02Color.BURGUNDY)},
        Type02Color.COTTON: STRICT_BEIGE_COLORS,
        Type02Color.GRAY: STRICT_GREY_COLORS,
        Type02Color.DARK_GRAY: DARK_GREY_COLORS,
        Type02Color.MUSTARD_YELLOW: MUSTARD_YELLOW_COLORS,
        Type02Color.REISINGERS_PINK: {(ShelfType.TYPE02, Type02Color.REISINGERS_PINK)},
        Type02Color.SAGE_GREEN: GREEN_COLORS,
        Type02Color.STONE_GRAY: STRICT_GREY_COLORS,
        Type02Color.WALNUT: {(ShelfType.TYPE02, Type02Color.WALNUT)},
        Type02Color.BLACK: {(ShelfType.TYPE02, Type02Color.BLACK)},
    },
    ShelfType.TYPE03: {
        Type03Color.WHITE: STRICT_WHITE_COLORS,
        Type03Color.BEIGE: STRICT_BEIGE_COLORS,
        Type03Color.GRAPHITE: {(ShelfType.TYPE03, Type03Color.GRAPHITE)},
        Type03Color.BEIGE_PINK: {(ShelfType.TYPE03, Type03Color.BEIGE_PINK)},
        Type03Color.WHITE_PINK: {(ShelfType.TYPE03, Type03Color.WHITE_PINK)},
        Type03Color.GRAPHITE_PINK: {(ShelfType.TYPE03, Type03Color.GRAPHITE_PINK)},
        Type03Color.GRAPHITE_WHITE: {(ShelfType.TYPE03, Type03Color.GRAPHITE_WHITE)},
        Type03Color.GRAPHITE_BEIGE: {(ShelfType.TYPE03, Type03Color.GRAPHITE_BEIGE)},
        Type03Color.WHITE_GRAPHITE: {(ShelfType.TYPE03, Type03Color.WHITE_GRAPHITE)},
        Type03Color.WHITE_BEIGE: {(ShelfType.TYPE03, Type03Color.WHITE_BEIGE)},
        Type03Color.BEIGE_GRAPHITE: {(ShelfType.TYPE03, Type03Color.BEIGE_GRAPHITE)},
        Type03Color.BEIGE_WHITE: {(ShelfType.TYPE03, Type03Color.BEIGE_WHITE)},
        Type03Color.GRAPHITE_STONE_GRAY: {
            (ShelfType.TYPE03, Type03Color.GRAPHITE_STONE_GRAY)
        },
        Type03Color.GRAPHITE_SAGE_GREEN: {
            (ShelfType.TYPE03, Type03Color.GRAPHITE_SAGE_GREEN)
        },
        Type03Color.GRAPHITE_MISTY_BLUE: {
            (ShelfType.TYPE03, Type03Color.GRAPHITE_MISTY_BLUE)
        },
        Type03Color.WHITE_STONE_GRAY: {
            (ShelfType.TYPE03, Type03Color.WHITE_STONE_GRAY),
        },
        Type03Color.WHITE_SAGE_GREEN: {
            (ShelfType.TYPE03, Type03Color.WHITE_SAGE_GREEN),
        },
        Type03Color.WHITE_MISTY_BLUE: {
            (ShelfType.TYPE03, Type03Color.WHITE_MISTY_BLUE),
        },
        Type03Color.CASHMERE_SAGE_GREEN: {
            (ShelfType.TYPE03, Type03Color.CASHMERE_SAGE_GREEN),
        },
        Type03Color.CASHMERE_MISTY_BLUE: {
            (ShelfType.TYPE03, Type03Color.CASHMERE_MISTY_BLUE),
        },
        Type03Color.CASHMERE_STONE_GRAY: {
            (ShelfType.TYPE03, Type03Color.CASHMERE_STONE_GRAY),
        },
    },
    ShelfType.TYPE13: {
        Type13Color.WHITE: STRICT_WHITE_COLORS,
        Type13Color.SAND: SAND_COLORS,
        Type13Color.MUSTARD_YELLOW: MUSTARD_YELLOW_COLORS,
        Type13Color.GRAY: STRICT_GREY_COLORS,
        Type13Color.DARK_GRAY: DARK_GREY_COLORS,
        Type13Color.WHITE_PLYWOOD: STRICT_WHITE_COLORS,
        Type13Color.GRAY_PLYWOOD: STRICT_GREY_COLORS,
        Type13Color.BLACK_PLYWOOD: BLACK_COLORS,
        Type13Color.CLAY_BROWN: {(ShelfType.TYPE13, Type13Color.CLAY_BROWN)},
        Type13Color.OLIVE_GREEN: {(ShelfType.TYPE13, Type13Color.OLIVE_GREEN)},
        Type13Color.BEIGE: STRICT_BEIGE_COLORS,
        Type13Color.BLACK: BLACK_COLORS,
    },
    ShelfType.VENEER_TYPE13: {
        VeneerType13Color.LIGHT: STRICT_BEIGE_COLORS,
        VeneerType13Color.DARK: {(ShelfType.VENEER_TYPE13, VeneerType13Color.DARK)},
    },
    ShelfType.TYPE23: {
        Type23Color.OFF_WHITE: STRICT_WHITE_COLORS,
        Type23Color.OYSTER_BEIGE: STRICT_BEIGE_COLORS,
        Type23Color.PISTACHIO_GREEN: GREEN_COLORS,
        Type23Color.INKY_BLACK: BLACK_COLORS,
        Type23Color.POWDER_PINK: RED_AND_PINK_COLORS,
    },
    ShelfType.TYPE24: {
        Type24Color.OFF_WHITE: STRICT_WHITE_COLORS,
        Type24Color.OYSTER_BEIGE: STRICT_BEIGE_COLORS,
        Type24Color.PISTACHIO_GREEN: GREEN_COLORS,
        Type24Color.INKY_BLACK: BLACK_COLORS,
        Type24Color.POWDER_PINK: RED_AND_PINK_COLORS,
    },
    ShelfType.TYPE25: {
        Type25Color.OFF_WHITE: STRICT_WHITE_COLORS,
        Type25Color.OYSTER_BEIGE: STRICT_BEIGE_COLORS,
        Type25Color.PISTACHIO_GREEN: GREEN_COLORS,
        Type25Color.INKY_BLACK: BLACK_COLORS,
        Type25Color.POWDER_PINK: RED_AND_PINK_COLORS,
    },
    ShelfType.SOFA_TYPE01: {
        Sofa01Color.REWOOL2_BROWN: {(ShelfType.SOFA_TYPE01, Sofa01Color.REWOOL2_BROWN)},
        Sofa01Color.REWOOL2_OLIVE_GREEN: {
            (ShelfType.SOFA_TYPE01, Sofa01Color.REWOOL2_OLIVE_GREEN)
        },
        Sofa01Color.REWOOL2_LIGHT_GRAY: {
            (ShelfType.SOFA_TYPE01, Sofa01Color.REWOOL2_LIGHT_GRAY)
        },
        Sofa01Color.REWOOL2_BUTTER_YELLOW: {
            (ShelfType.SOFA_TYPE01, Sofa01Color.REWOOL2_BUTTER_YELLOW)
        },
        Sofa01Color.REWOOL2_SHADOW_PINK: {
            (ShelfType.SOFA_TYPE01, Sofa01Color.REWOOL2_SHADOW_PINK)
        },
        Sofa01Color.REWOOL2_GREEN: {(ShelfType.SOFA_TYPE01, Sofa01Color.REWOOL2_GREEN)},
        Sofa01Color.REWOOL2_BABY_BLUE: {
            (ShelfType.SOFA_TYPE01, Sofa01Color.REWOOL2_BABY_BLUE)
        },
        Sofa01Color.CORDUROY_ECRU: {(ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_ECRU)},
        Sofa01Color.CORDUROY_ROCK: {(ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_ROCK)},
        Sofa01Color.CORDUROY_DARK_BROWN: {
            (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_DARK_BROWN)
        },
        Sofa01Color.CORDUROY_STEEL: {
            (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_STEEL)
        },
        Sofa01Color.CORDUROY_TOBACCO: {
            (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_TOBACCO)
        },
        Sofa01Color.CORDUROY_PINK: {(ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_PINK)},
        Sofa01Color.CORDUROY_CAMOUFLAGE: {
            (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_CAMOUFLAGE)
        },
        Sofa01Color.CORDUROY_BLUE_KLEIN: {
            (ShelfType.SOFA_TYPE01, Sofa01Color.CORDUROY_BLUE_KLEIN)
        },
    },
}
