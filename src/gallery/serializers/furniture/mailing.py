from typing import Optional
from urllib.parse import urljoin

from django.conf import settings
from django.utils.translation import gettext as _
from rest_framework import serializers

from custom.enums import (
    Furniture,
    ShelfType,
)
from gallery.services.prices_for_serializers import RegionCurrencySerializerMixin


class MailingItemBaseSerializer(serializers.Serializer):
    furniture_type = serializers.CharField()
    description = serializers.JSONField(source='get_item_description')
    item_url = serializers.SerializerMethodField()
    image_url = serializers.SerializerMethodField()
    rectangular_image_url = serializers.SerializerMethodField()

    class Meta:
        fields = (
            'furniture_type',
            'description',
            'image_url',
            'item_url',
            'rectangular_image_url',
        )

    def get_item_url(self, obj) -> str:
        return obj.get_url_with_region(self.context['region'])

    @staticmethod
    def get_image_url(obj) -> str:
        """Get preview url for furniture or sample box.

        Since we store images for furniture in amazon s3 and for sample boxes we use
        static images, the url is retrieved in a different way.
        """
        preview_url = getattr(obj.preview, 'url', '')
        if preview_url and obj.furniture_type == Furniture.sample_box.value:
            return urljoin(settings.SITE_URL, preview_url)

        return preview_url

    @staticmethod
    def get_rectangular_image_url(obj) -> str | None:
        if obj.furniture_type == Furniture.sample_box.value:
            return
        elif unreal := getattr(obj.s4l_unreal_rectangular, 'image', None):
            return unreal.url
        else:
            return getattr(obj.preview, 'url', None)


class MailingSellableItemSerializer(MailingItemBaseSerializer):
    category = serializers.SerializerMethodField()
    color = serializers.SerializerMethodField()
    wooden_material = serializers.SerializerMethodField()
    height = serializers.IntegerField()
    width = serializers.IntegerField()
    depth = serializers.IntegerField()
    max_load = serializers.IntegerField(source='get_max_load')
    compartment_max_load = serializers.SerializerMethodField()

    class Meta:
        fields = (
            *MailingItemBaseSerializer.Meta.fields,
            'category',
            'color',
            'wooden_material',
            'height',
            'width',
            'depth',
            'max_load',
            'compartment_max_load',
        )

    @staticmethod
    def get_category(obj) -> Optional[str]:
        return (
            obj.furniture_category.translated_name if obj.furniture_category else None
        )

    @staticmethod
    def get_color(obj) -> Optional[str]:
        if obj.furniture_type == Furniture.sample_box.value:
            return None

        return obj.color.translated_color

    @staticmethod
    def get_wooden_material(obj) -> Optional[str]:
        if obj.furniture_type in (Furniture.sample_box.value, Furniture.sotty.value):
            return None

        return obj.get_material() or _('particleboard_t13')

    @staticmethod
    def get_compartment_max_load(obj) -> Optional[int]:
        if obj.furniture_type != Furniture.jetty.value:
            return None

        return obj.get_compartment_max_load()


class MailingSavedFurnitureSerializer(
    RegionCurrencySerializerMixin,
    MailingItemBaseSerializer,
):
    """Serializer used in mailings to retrieve essential data about saved furniture.

    Designed to work with each FurnitureAbstract subclass.
    """

    title = serializers.SerializerMethodField()
    furniture_type = serializers.SerializerMethodField()
    region_price_display = serializers.SerializerMethodField()
    description = serializers.JSONField(source='get_item_description')
    image_type = serializers.SerializerMethodField()

    class Meta:
        fields = (
            *MailingItemBaseSerializer.Meta.fields,
            'title',
            'furniture_type',
            'region_price_display',
            'image_type',
            'description',
        )

    @staticmethod
    def get_title(obj):
        return (
            f'{obj.furniture_category.translated_name} '
            f'{ShelfType(obj.shelf_type).translated_name}'
        )

    @staticmethod
    def get_furniture_type(obj):
        return obj.furniture_type

    def get_region_price_display(self, obj):
        price = obj.get_regionalized_price(region=self.region)
        cached_region_data = self.region.cached_region_data
        return cached_region_data.get_format_price(price)

    @staticmethod
    def _get_image_info(obj) -> tuple[str | None, str]:
        if unreal_scene := getattr(obj.s4l_unreal_scene, 'image', None):
            return unreal_scene.url, 'unreal_scene'
        else:
            return getattr(obj.preview, 'url', None), 'preview'

    def get_image_url(self, obj) -> str:
        image_url, _ = self._get_image_info(obj)
        return image_url

    def get_image_type(self, obj) -> str:
        _, image_type = self._get_image_info(obj)
        return image_type
