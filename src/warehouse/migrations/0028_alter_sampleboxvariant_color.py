# Generated by Django 4.2.23 on 2025-08-13 11:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('warehouse', '0027_alter_sampleboxvariant_variant_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='sampleboxvariant',
            name='color',
            field=models.PositiveSmallIntegerField(blank=True, choices=[(0, 'T01 White / T02 White / T03 Exterior White / T01 Veneer Ash / T13 White / T13 Veneer Light / Tone Expressions Off White / Sofa Rewool2 Brown'), (1, 'T01 Black / T02 Terracotta / T03 Exterior Beige / T01 Veneer Oak / T13 Veneer Dark / Tone Expressions Oyster Beige / Sofa Rewool2 Olive Green'), (3, 'T01 Grey / T02 Sand / T03 White / T13 Gray / Tone Expressions Inky Black / Sofa Rewool2 Butter Yellow'), (7, 'T01 Yellow / T02 Sky Blue / T03 Stone Gray / Sofa Corduroy Ecru'), (8, 'T01 Dusty Pink / T02 Burgundy / T03 Sage Green / T13 Clay Brown / Sofa Corduroy Rock'), (9, 'T01 Blue / T02 Cotton / T03 Misty Blue / T13 Olive Green / Sofa Corduroy Dark Brown'), (11, 'T01 Moss Green / T13 Black / Sofa Corduroy Tobacco'), (2, 'T02 Midnight Blue / T03 Exterior Graphite / T01 Veneer Dark Oak / Tone Expressions Pistachio Green / Sofa Rewool2 Light Gray'), (6, 'T02 Matte Black / T03 Pink / T13 Gray Plywood / Sofa Rewool2 Baby Blue'), (10, 'T02 Gray / T13 Beige / Sofa Corduroy Steel'), (15, 'T02 Reisingers Pink'), (16, 'T02 Sage Green'), (17, 'T02 Stone Gray'), (19, 'T02 Black'), (4, 'T03 Beige / Tone Expressions Powder Pink / Sofa Rewool2 Shadow Pink'), (5, 'T03 Graphite / T13 White Plywood / Sofa Rewool2 Green'), (12, 'Sofa Corduroy Pink'), (13, 'Sofa Corduroy Camouflage'), (14, 'Sofa Corduroy Blue Klein')], null=True),
        ),
    ]
