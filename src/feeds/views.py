from django.http import HttpResponse
from django.views.generic.base import View
from rest_framework.generics import get_object_or_404

from feeds.models import (
    Feed,
)


class FeedFileView(View):
    authentication_classes = ()
    permission_classes = ()

    def get(self, request, *args, **kwargs):
        instance = get_object_or_404(Feed, id=self.kwargs['id'])
        response = HttpResponse(
            instance.file,
            content_type='application/csv',
        )
        response['Content-Disposition'] = 'attachment; filename="{0}"'.format(
            instance.file.name,
        )
        return response
