from urllib.parse import urljoin

from django.conf import settings
from django.contrib import (
    admin,
    messages,
)
from django.db.models import Count
from django.http import HttpResponseRedirect
from django.urls import (
    path,
    reverse,
)
from django.utils.html import format_html
from django.views import View

from custom.admin_action import admin_action_with_form
from feeds.forms import (
    AddFurnitureByIdsForm,
    FeedCategoryAdminForm,
    FeedForm,
)
from feeds.models import (
    Feed,
    FeedCategory,
    FeedImage,
    FeedItem,
)
from feeds.services.generate_feed_items import generate_feed_items
from feeds.tasks import (
    generate_feed_file,
    generate_lacking_unreal_render_tasks_for_feed_by_ids,
    sync_furniture_with_categories,
    sync_real_photo_images,
    update_feed_items_margin,
)
from feeds.utils import get_items_with_proper_images_count_for_feed
from regions.models import Region
from taskapp.celery import tasks_in_queue


class RegionFilter(admin.SimpleListFilter):
    title = 'Region'

    parameter_name = 'region'

    def lookups(self, request, model_admin):
        return list(Region.objects.values_list('id', 'name').order_by('name'))

    def queryset(self, request, queryset):
        if value := self.value():
            return queryset.filter(region=value)


class FeedAdmin(admin.ModelAdmin):
    form = FeedForm

    list_display = [
        'id',
        'name',
        'get_file',
        'generated_images_count',
        'region',
        'language',
        'commerce_system',
        'updated_at',
        'file_processing_time',
        'file_status',
        'file_created_at',
        'regenerate_automatically',
    ]

    filter_horizontal = ('categories',)

    list_filter = [
        RegionFilter,
        'language',
        'commerce_system',
        'categories',
    ]

    actions = [
        'create_feed_file',
        'sync_real_photo_images',
        'order_lacking_unreal_images',
    ]

    @admin.display(description='File')
    def get_file(self, obj):
        if not obj.file:
            return ''
        url = urljoin(
            settings.SITE_URL,
            reverse('feed_file', args=[obj.id]),
        )
        return format_html(f'<a href="{url}" target="_blank">{url}</a>')

    def get_queryset(self, request):
        return (
            super()
            .get_queryset(request)
            .annotate(
                feed_items_count=Count('categories__items'),
            )
        )

    def generated_images_count(self, obj: Feed) -> str:
        return (
            f'{get_items_with_proper_images_count_for_feed(obj)}/{obj.feed_items_count}'
        )

    @admin.action(description='Regenerate feed file')
    def create_feed_file(self, request, queryset):
        for feed in queryset:
            generate_feed_file.delay(feed.id)

    @admin.action(description='Order lacking real photos')
    def sync_real_photo_images(self, request, queryset):
        sync_real_photo_images.delay(list(queryset.values_list('pk', flat=True)))

    @admin.action()
    def order_lacking_unreal_images(self, request, queryset):
        messages.info(
            request,
            'Unreal images ordered, it might take a while',
        )

        generate_lacking_unreal_render_tasks_for_feed_by_ids.delay(
            list(queryset.values_list('id', flat=True))
        )

    def get_urls(self):
        return [  # noqa: RUF005
            path(
                'update_margin/',
                self.admin_site.admin_view(UpdateFeedsMarginsAdminView.as_view()),
                name='update-feeds-margins',
            ),
        ] + super().get_urls()


class FeedImageAdmin(admin.ModelAdmin):
    list_display = [
        'id',
        'image',
        'categories',
        'config',
        'images_preview',
    ]
    list_filter = ['config']
    search_fields = ['id', 'items__object_id']
    autocomplete_fields = ['items']

    @admin.display
    def images_preview(self, obj):
        if image := obj.image:
            return format_html(
                f'<div style="width:250px; float:left"><img src="{image.url}" '
                'width="300px" /></div>'
            )
        return ''

    @admin.display(description='Categories')
    def categories(self, obj: FeedImage) -> str:
        category_names = set(obj.items.all().values_list('category__name', flat=True))
        return ', '.join(sorted(category_names))


class FeedItemAdmin(admin.ModelAdmin):
    list_display = [
        'id',
        'furniture',
        'furniture_category_id',
        'category',
        'images_preview',
    ]
    list_filter = ['category', 'content_type']
    search_fields = ['object_id']

    actions = [
        'get_related_feed_images',
    ]

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        return queryset.prefetch_related('images').select_related('category')

    @admin.display
    def images_preview(self, obj):
        return format_html(
            ''.join(
                [
                    f'<div style="width:300px; float:left"><img src='
                    f'"{image.image.url}" width="300px" /></div>'
                    for image in obj.images.all()
                    if image.image
                ]
            )
        )

    @admin.action
    def get_related_feed_images(self, request, queryset):
        ids = [
            str(image_id)
            for item in queryset
            for image_id in item.images.values_list('pk', flat=True)
        ]
        url = f'{reverse("admin:feeds_feedimage_changelist")}?id__in={",".join(ids)}'
        return HttpResponseRedirect(url)


class FeedCategoryAdmin(admin.ModelAdmin):
    list_display = [
        'id',
        'name',
        'furniture',
        'related_furniture_number',
        'related_feeds',
        'auto_furniture_sync',
    ]
    list_filter = ['feeds']

    form = FeedCategoryAdminForm

    actions = [
        'sync_furniture',
        'add_furniture_by_ids',
    ]

    @admin.display
    def related_furniture_number(self, obj):
        return obj.items.count()

    @admin.display
    def related_feeds(self, obj):
        return ', '.join([str(feed) for feed in obj.feeds.all()])

    @admin.display(description='Link to feed items')
    def furniture(self, obj):
        url = (
            reverse('admin:feeds_feeditem_changelist')
            + f'?category__id__exact={obj.id}'
        )
        return format_html('<a href="{}">Link</a>', url)

    @admin.action(description='Sync furniture with categories')
    def sync_furniture(self, request, queryset):
        for category in queryset:
            sync_furniture_with_categories.delay(category.id)

    @admin.action
    def add_furniture_by_ids(self, request, queryset):
        return admin_action_with_form(
            modeladmin=self,
            request=request,
            queryset=queryset,
            form_class=AddFurnitureByIdsForm,
            success_function=generate_feed_items,
            success_function_kwargs={},
        )


class UpdateFeedsMarginsAdminView(View):
    def get(self, request, *args, **kwargs):
        task_in_queue = tasks_in_queue(
            [
                'feeds.tasks.update_feed_items_margin',
                'feeds.tasks.update_one_product_margin_task',
            ]
        )
        if not task_in_queue:
            update_feed_items_margin.delay()

        messages.info(
            request,
            'Margins are updating. This takes a while.',
        )

        return HttpResponseRedirect(reverse('admin:feeds_feed_changelist'))


admin.site.register(FeedImage, FeedImageAdmin)
admin.site.register(FeedItem, FeedItemAdmin)
admin.site.register(FeedCategory, FeedCategoryAdmin)
admin.site.register(Feed, FeedAdmin)
