# Generated by Django 4.2.23 on 2025-08-14 18:31

import django.core.validators
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('feeds', '0036_alter_feed_commerce_system'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='feedcategory',
            name='image_config',
        ),
        migrations.RemoveField(
            model_name='feedcategory',
            name='is_moved_from_board',
        ),
        migrations.AddField(
            model_name='feedimage',
            name='image_jpg',
            field=models.ImageField(blank=True, max_length=200, null=True, upload_to='feeds/feed_image/%Y/%m', validators=[django.core.validators.FileExtensionValidator(['jpg'])]),
        ),
        migrations.AddField(
            model_name='feedimage',
            name='image_webp',
            field=models.ImageField(blank=True, max_length=200, null=True, upload_to='feeds/feed_image/%Y/%m', validators=[django.core.validators.FileExtensionValidator(['webp'])]),
        ),
        migrations.AlterField(
            model_name='feed',
            name='commerce_system',
            field=models.CharField(choices=[('google', 'Google'), ('fb', 'Fb'), ('criteo', 'Criteo'), ('amazon', 'Amazon'), ('synerise', 'Synerise'), ('awim', 'Awim')], max_length=12),
        ),
        migrations.AlterField(
            model_name='feedimage',
            name='additional_data',
            field=models.JSONField(blank=True, default=dict),
        ),
        migrations.AlterField(
            model_name='feedimage',
            name='config',
            field=models.PositiveSmallIntegerField(choices=[(303, 'Unreal scene'), (401, 'Real photo'), (101, 'Blender, front, door closed'), (102, 'Blender, front, door half open'), (103, 'Blender, front, door open'), (104, 'Blender, left 30, door closed'), (105, 'Blender, left 30, door half open'), (106, 'Blender, left 30, door open'), (107, 'Blender, right 30, door closed'), (108, 'Blender, right 30, door half open'), (109, 'Blender, right 30, door open'), (201, 'WebGL front'), (204, 'WebGL left'), (207, 'WebGL right'), (301, 'Unreal front studio'), (302, 'Unreal left studio')]),
        ),
    ]
