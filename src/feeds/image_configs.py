from django.db import models


class ImageConfigOption(models.IntegerChoices):
    UNREAL_SCENE = 303, 'Unreal scene'
    REAL_PHOTO = 401, 'Real photo'

    # All below are deprecated, remove in next PR
    BLENDER_FRONT_DOOR_CLOSED = 101, '<PERSON>len<PERSON>, front, door closed'
    BLENDER_FRONT_DOOR_HALF_OPEN = 102, '<PERSON>len<PERSON>, front, door half open'
    BLENDER_FRONT_DOOR_OPEN = 103, 'Blender, front, door open'

    BLENDER_LEFT30_DOOR_CLOSED = 104, 'Blen<PERSON>, left 30, door closed'
    BLENDER_LEFT30_DOOR_HALF_OPEN = 105, '<PERSON>len<PERSON>, left 30, door half open'
    BLENDER_LEFT30_DOOR_OPEN = 106, 'Blen<PERSON>, left 30, door open'

    BLENDER_RIGHT30_DOOR_CLOSED = 107, 'Blender, right 30, door closed'
    BLENDER_RIGHT30_DOOR_HALF_OPEN = 108, '<PERSON>len<PERSON>, right 30, door half open'
    BLENDER_RIGHT30_DOOR_OPEN = 109, '<PERSON>len<PERSON>, right 30, door open'

    WEBGL_FRONT = 201, 'WebGL front'
    WEBGL_LEFT30 = 204, 'WebGL left'
    WEBGL_RIGHT30 = 207, 'WebGL right'

    UNREAL_FRONT_STUDIO = 301, 'Unreal front studio'
    UNREAL_LEFT30_STUDIO = 302, 'Unreal left studio'

    @classmethod
    def webgl_configs(cls) -> list['ImageConfigOption']:
        return [
            cls.WEBGL_FRONT,
            cls.WEBGL_LEFT30,
            cls.WEBGL_RIGHT30,
        ]

    @classmethod
    def webgl_config_choices(cls) -> list[tuple[int, str]]:
        return [(config.value, config.name) for config in cls.webgl_configs()]
