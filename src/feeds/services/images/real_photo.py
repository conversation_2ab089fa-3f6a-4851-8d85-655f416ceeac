from django.db.models import QuerySet

from catalogue.models import CatalogueEntry
from feeds.image_configs import ImageConfigOption
from feeds.models import FeedImage, FeedItem


class RealPhotoImagesService:
    def __init__(self, feed_ids: list[int]) -> None:
        self.feed_ids = feed_ids

    def _get_items_without_real_photos(self) -> QuerySet[FeedItem]:
        return (
            FeedItem.objects.filter(category__feeds__in=self.feed_ids)
            .exclude(
                images__config=ImageConfigOption.REAL_PHOTO, images__image__isnull=False
            )
            .select_related('content_type')
        )

    def _get_catalogue_entries_with_real_photos(
        self,
        feed_items: QuerySet[FeedItem],
    ) -> dict[tuple[int, int], CatalogueEntry]:
        """Done to hit CatalogueEntry table once"""
        content_type_object_pairs = {
            (item.content_type_id, item.object_id) for item in feed_items
        }

        if not content_type_object_pairs:
            return {}

        catalogue_entries = CatalogueEntry.objects.filter(
            real_lifestyle_image__isnull=False,
            object_id__in={pair[1] for pair in content_type_object_pairs},
            content_type_id__in={pair[0] for pair in content_type_object_pairs},
        ).select_related('content_type')

        entries_lookup = {}
        for entry in catalogue_entries:
            key = (entry.content_type_id, entry.object_id)
            if key in content_type_object_pairs:
                entries_lookup[key] = entry

        return entries_lookup

    @staticmethod
    def _create_or_update_feed_image(
        feed_item: FeedItem, catalogue_entry: CatalogueEntry
    ) -> None:
        if not catalogue_entry.real_lifestyle_image.name:
            return

        feed_image, created = FeedImage.objects.get_or_create(
            config=ImageConfigOption.REAL_PHOTO,
            items__content_type=feed_item.content_type,
            items__object_id=feed_item.object_id,
        )

        if created:
            feed_image.items.add(feed_item)

        if not feed_image.image.name:
            feed_image.image.name = catalogue_entry.real_lifestyle_image.name
            feed_image.save()

    def update_images_with_real_photos(self) -> None:
        feed_items = self._get_items_without_real_photos()
        catalogue_entries_lookup = self._get_catalogue_entries_with_real_photos(
            feed_items
        )

        for feed_item in feed_items:
            key = (feed_item.content_type_id, feed_item.object_id)
            catalogue_entry = catalogue_entries_lookup.get(key)
            if catalogue_entry:
                self._create_or_update_feed_image(feed_item, catalogue_entry)
