import pytest

from feeds.image_configs import ImageConfigOption


@pytest.fixture
def jetty_feed(
    feeds_category_factory,
    feeds_item_factory,
    feeds_factory,
    feeds_image_factory,
    jetty,
):
    feed_category = feeds_category_factory()
    feed_item = feeds_item_factory(category=feed_category, furniture=jetty)
    feed = feeds_factory()
    feed.categories.add(feed_category)
    feed_image = feeds_image_factory(config=ImageConfigOption.UNREAL_SCENE)
    feed_image.items.add(feed_item)
    return feed, feed_category, feed_item


@pytest.fixture
def watty_feed(
    feeds_category_factory,
    feeds_item_factory,
    feeds_factory,
    feeds_image_factory,
    watty,
):
    feed_category = feeds_category_factory()
    feed_item = feeds_item_factory(category=feed_category, furniture=watty)
    feed = feeds_factory()
    feed.categories.add(feed_category)
    feed_image = feeds_image_factory(config=ImageConfigOption.UNREAL_SCENE)
    feed_image.items.add(feed_item)
    return feed, feed_category, feed_item


@pytest.fixture
def sotty_feed(
    feeds_category_factory,
    feeds_item_factory,
    feeds_factory,
    feeds_image_factory,
    sotty,
):
    feed_category = feeds_category_factory()
    feed_item = feeds_item_factory(category=feed_category, furniture=sotty)
    feed = feeds_factory()
    feed.categories.add(feed_category)
    feed_image = feeds_image_factory(config=ImageConfigOption.UNREAL_SCENE)
    feed_image.items.add(feed_item)
    return feed, feed_category, feed_item
