import { en, fr, de, es, nl, pl, sv, da, it, nb as no } from '@formkit/i18n';
import { defineFormKitConfig } from '@formkit/vue';
import { generateClasses } from '@formkit/themes';
import DOMPurify from 'dompurify';
import { createFormKitInputsPlugin } from '@kgierke/formkit-inputs';
import formkitTheme from './formkit/formkit-theme.js';
import { tyText, tyTextArea, tyPassword, tyBox, tySelect, tyNumber } from './formkit/formkit-inputs';
import stepNumber from './formkit/step-number';

const hasUpperAndLowerCase = (node: any) => {
  const value = String(node.value || '');
  const hasUpperCase = /[A-Z]/.test(value);
  const hasLowerCase = /[a-z]/.test(value);
  return hasUpperCase && hasLowerCase;
};

const hasNumbersOrSymbols = (node: any) => {
  const value = String(node.value || '');
  const hasNumber = /[0-9]/.test(value);
  const hasSymbol = /[!@#$%^&*(),.?":{}|<>]/.test(value);
  return hasNumber || hasSymbol;
};

const formKitInputPlugin = createFormKitInputsPlugin();

const hasNoSpaces = (node: any) => {
  // Check if the first or last character is a space
  if (!node.value || String(node.value).startsWith(' ') || String(node.value).endsWith(' ')) {
    return false;
  }

  return true;
};

/**
 * A little plugin that automatically scrolls to the first error.
 **/
function scrollToErrors (node: any) {
  if (node.props.type === 'form') {
    function scrollTo (node: any) {
      const el = document.getElementById(node.props.id);

      if (el) {
        el.scrollIntoView({ behavior: 'smooth', block: 'end', inline: 'nearest' });
        el.focus({ preventScroll: true });
      }
    }

    function scrollToErrors () {
      node.walk((child: any) => {
        // Check if this child has errors
        if (child.ledger.value('blocking') || child.ledger.value('errors')) {
          // We found an input with validation errors
          scrollTo(child);
          // Stop searching
          return false;
        }
      }, true);
    }

    const onSubmitInvalid = node.props?.onSubmitInvalid;

    node.props.onSubmitInvalid = () => {
      onSubmitInvalid && onSubmitInvalid(node);
      scrollToErrors();
    };

    node.on('unsettled:errors', scrollToErrors);
  }

  return false;
}

// Prevent Formkit from clearing the input value when the node is created
// This is useful when you start typing in an input and then hydration happens and removes the value
function initFormValues (node: any) {
  if (node.type !== 'group') {
    if (import.meta.client) {
      node.on('created', () => {
        const value = (document?.getElementById(node.context.id) as HTMLInputElement)?.value;
        value && node.input(value);
      });
    }
  }
}

// Plugin to strip HTML tags from input values
const sanitizePlugin = (node: FormKitNode) => {
  const sanitizeInput = (input: unknown) => {
    if (typeof input !== 'string') { return input; }

    if (process.client) {
      return DOMPurify.sanitize(input, {
        ALLOWED_TAGS: [],
        ALLOWED_ATTR: []
      });
    }

    return input;
  };

  if (['text', 'textarea', 'tyText', 'tyTextArea', 'tyPassword', 'tyNumber'].includes(node.props.type)) {
    node.hook.input((payload) => {
      if (typeof payload === 'string') {
        const sanitized = sanitizeInput(payload);
        return sanitized !== payload ? sanitized : payload;
      }

      return payload;
    });
  }
};

export default defineFormKitConfig(() => {
  return {
    plugins: [scrollToErrors, initFormValues, sanitizePlugin, formKitInputPlugin],
    locales: { en, fr, de, es, nl, pl, sv, da, it, no },
    locale: 'en',
    config: {
      classes: generateClasses(formkitTheme)
    },
    inputs: {
      tyText,
      tyNumber,
      tyTextArea,
      tyPassword,
      tyBox,
      tySelect,
      stepNumber
    },
    rules: {
      hasUpperAndLowerCase,
      hasNumbersOrSymbols,
      hasNoSpaces
    }
  };
});
