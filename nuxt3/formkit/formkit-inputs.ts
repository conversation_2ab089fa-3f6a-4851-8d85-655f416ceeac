import { createInput } from '@formkit/vue';

export const tyNumber = createInput(
  [
    {
      $el: 'input',
      bind: '$attrs',
      attrs: {
        id: '$id',
        placeholder: '$label',
        disabled: '$disabled',
        onInput: '$handlers.DOMInput',
        onBlur: '$handlers.blur',
        // >>>> KLUCZOWE: podpinasz własne handlery
        onKeydown: '$handlers.onNumberKeydown',
        onBeforeinput: '$handlers.onNumberBeforeInput',
        onPaste: '$handlers.onNumberPaste',

        class: '$:$classes.input + " peer"',
        value: '$_value',
        type: 'number',
        min: '$min',
        max: '$max',
        step: '$step',
        inputmode: 'numeric'
      }
    },
    {
      $cmp: 'IconFormulateClear',
      props: {
        type: 'button',
        class: '$classes.clear',
        onClick: '$handlers.clear'
      }
    }
  ],
  {
    family: 'tyNumber',
    props: {
      min: { type: [Number, String], default: undefined },
      max: { type: [Number, String], default: undefined },
      step: { type: [Number, String], default: undefined }
    },
    features: [
      (node) => {
        node.on('created', () => {
          const h = node.context?.handlers as any;
          if (!h) { return; }

          h.onNumberKeydown = (e: KeyboardEvent) => {
            const bad = ['e', 'E', '+', '-'];

            if (e.key === 'ArrowUp' || e.key === 'ArrowDown' || bad.includes(e.key)) {
              e.preventDefault();
            }
          };

          h.onNumberBeforeInput = (e: InputEvent) => {
            if (e.data && /[eE+\-]/.test(e.data)) {
              e.preventDefault();
            }
          };

          h.onNumberPaste = (e: ClipboardEvent) => {
            const t = e.clipboardData?.getData('text') ?? '';

            if (!/^\d*(?:[.,]\d+)?$/.test(t)) {
              e.preventDefault();
            }
          };

          // Clear
          h.clear = () => node.input('');
        });
      }
    ]
  },
  {
    help: {
      $el: 'div',
      if: '$state.invalid != true && $help',
      attrs: {
        class: '$:$classes.help + " flex items-center gap-4"'
      },
      children: [
        { $cmp: 'IconAlert', props: { class: 'w-24 h-24' } },
        '$help'
      ]
    }
  }
);

export const tyText = createInput([
  {
    $el: 'input',
    bind: '$attrs',
    attrs: {
      id: '$id',
      placeholder: '$label',
      disabled: '$disabled',
      onInput: '$handlers.DOMInput',
      onBlur: '$handlers.blur',
      class: '$:$classes.input + " peer"',
      value: '$_value',
      type: '$inputType'
    }
  },
  {
    $cmp: 'IconFormulateClear',
    props: {
      type: 'button',
      class: '$classes.clear',
      onClick: '$handlers.clear'
    }
  },
  {
    if: '$slots.button',
    then: '$slots.button'
  }
],
{
  family: 'tyText',
  features: [(node) => {
    node.on('created', () => {
      if (node.context?.handlers) {
        node.context.handlers.clear = () => {
          node.input('');
        };
      }
    });
  }]
},
{
  help: {
    $el: 'div',
    if: '$state.invalid != true && $help',
    attrs: {
      class: '$:$classes.help + " flex items-center gap-4"'
    },
    children: [
      {
        $cmp: 'IconAlert',
        props: {
          class: 'w-24 h-24'
        }
      },
      '$help'
    ]
  }
});

export const tyTextArea = createInput([
  {
    $el: 'textarea',
    bind: '$attrs',
    attrs: {
      id: '$id',
      rows: '$rows',
      disabled: '$disabled',
      onInput: '$handlers.DOMInput',
      onBlur: '$handlers.blur',
      class: '$:$classes.input + " peer"',
      value: '$_value'
    }
  }
], {
  family: 'tyTextArea'
},
{
  help: {
    $el: 'div',
    if: '$state.invalid != true && $help',
    attrs: {
      class: '$:$classes.help + " flex items-center gap-4"'
    },
    children: [
      {
        $cmp: 'IconAlert',
        props: {
          class: 'w-24 h-24'
        }
      },
      '$help'
    ]
  }
});

const showPasswordValidation = (node: any) => {
  node.on('created', () => {
    if (node.props.validationHints) {
      const updateHints = () => {
        node.props.parsedRules.forEach((rule: any, index: any) => {
          if (node.context?.state) {
            node.context.state.validationHints[index] = {
              name: node.props.validationMessages[rule.name],
              state: rule.state,
              isHint: rule.name !== 'hasNoSpaces'
            };
          }
        });
      };

      if (node.context?.state) {
        node.context.state.validationHints = [];
        updateHints();
        node.on('message-updated', () => {
          if (node.props.validationHints) {
            updateHints();
          }
        });
      }
    }
  });
};

export const tyPassword = createInput([
  {
    $el: 'input',
    bind: '$attrs',
    attrs: {
      id: '$id',
      placeholder: '$label',
      disabled: '$disabled',
      type: '$state.showPassword',
      onInput: '$handlers.DOMInput',
      onBlur: '$handlers.blur',
      class: '$:$classes.input + " peer"',
      value: '$_value'
    }
  },
  {
    $cmp: 'IconFormulateEyeClose',
    props: {
      type: 'button',
      class: '$classes.showPassword',
      onClick: '$handlers.togglePassword'
    }
  }
], {
  props: {
    validationHints: {
      boolean: true,
      default: false
    }
  },
  family: 'tyText',
  features: [showPasswordValidation, (node: any) => {
    node.on('created', () => {
      if (node.context?.state) {
        (node.context.state as any).showPassword = 'password';

        if (node.context?.handlers) {
          node.context.handlers.togglePassword = () => {
            if (node.context?.state) {
              const state = node.context.state as any;
              state.showPassword = state.showPassword === 'password' ? 'text' : 'password';
            }
          };
        }
      }
    });
  }]
},
{
  help: {
    $el: 'ul',
    if: '$validationHints',
    attrs: {
      class: '$classes.help'
    },
    children: [
      {
        $el: 'li',
        attrs: {
          class: 'text-error-500'
        },
        for: ['item', 'key', '$state.validationHints'],
        if: '$item.isHint === false && $item.state == false',
        children: '$item.name'
      },
      {
        $el: 'li',
        attrs: {
          class: 'flex items-center gap-8'
        },
        for: ['item', 'key', '$state.validationHints'],
        if: '$item.isHint',
        children: [
          {
            if: '$item.state == true || $state.submitted == false',
            $cmp: 'IconFormulateValidTick',
            props: {
              class: {
                if: '$item.state == true',
                then: 'text-success-500',
                else: 'text-neutral-500'
              }
            }
          },
          {
            if: '$item.state == false && $state.submitted == true',
            $cmp: 'IconFormulateNotValid',
            props: {
              class: {
                if: '$item.state == true',
                then: 'text-success-500',
                else: 'text-error-500'
              }
            }
          },
          {
            $el: 'span',
            children: '$item.name'
          }
        ]
      }
    ]
  },
  messages: {
    if: '$validationHints == false'
  }
});

export const tyBox = createInput(
  [
    {
      $el: 'label',
      bind: '$attrs',
      attrs: {
        for: '$id',
        class: '$classes.label'
      },
      children: [
        {
          $el: 'input',
          bind: '$attrs',
          attrs: {
            disabled: '$disabled',
            checked: '$_value',
            onBlur: '$handlers.blur',
            value: '$_value',
            type: 'checkbox',
            id: '$id',
            onInput: '$handlers.toggleChecked',
            class: '$: $classes.input + " flex-none"'
          }
        },
        {
          $el: 'span',
          attrs: {
            class: 'w-full'
          },
          if: '$label || $slots.labelExtra',
          then: '$label',
          children: [
            {
              if: '$label',
              then: '$label'
            }, {
              if: '$slots.labelExtra',
              then: '$slots.labelExtra'
            }
          ]
        }
      ]
    }
  ], {
    family: 'tyBox',
    features: [(node) => {
      node.on('created', () => {
        if (node.context?.handlers) {
          node.context.handlers.toggleChecked = (e: Event) => {
            const el = e?.target;

            if (el instanceof HTMLInputElement) {
              if (el.checked) {
                node.input(true);
              } else {
                node.input(false);
              }
            }
          };
        }
      });
    }]
  }, {
    label: null
  });

export const tySelect = createInput([
  {
    $el: 'div',
    bind: '$attrs',
    if: '$slots.icon',
    attrs: {
      class: 'absolute top-28 transform -translate-y-[6px] left-16 pointer-events-none'
    },
    children: [
      '$slots.icon'
    ]
  },
  {
    $el: 'select',
    bind: '$attrs',
    attrs: {
      id: '$id',
      disabled: '$disabled',
      onInput: '$handlers.DOMInput',
      onBlur: '$handlers.blur',
      class: '$: $classes.input + " " + $classes.selectIconExtra',
      value: '$_value'
    },
    children: [
      {
        $el: 'option',
        if: '$placeholder',
        attrs: {
          value: '',
          disabled: true,
          selected: '!$_value'
        },
        children: '$placeholder'
      },
      {
        $el: 'option',
        for: ['option', '$options'],
        attrs: {
          selected: '$option.value == $_value',
          value: '$option.value'
        },
        children: '$option.label'
      }
    ]
  },
  {
    $cmp: 'IconCaretDown',
    props: {
      type: 'button',
      class: '$classes.dropdown'
    }
  }
], {
  family: 'tySelect',
  props: ['options', 'placeholder']
},
{
  help: {
    $el: 'div',
    if: '$state.invalid != true && $help',
    attrs: {
      class: '$:$classes.help + " flex items-center gap-4"'
    },
    children: [
      {
        $cmp: 'IconAlert',
        props: {
          class: 'w-24 h-24'
        }
      },
      '$help'
    ]
  }
});
