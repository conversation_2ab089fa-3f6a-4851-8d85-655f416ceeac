import { type RegionApiData, useGlobal } from '~/stores/global';
import { PROMOTION } from '~/api/promotion';
import { REVIEWS_GLOBAL } from '~/api/reviews';
import { useApi } from '~/composables/useApi';

export default defineNuxtRouteMiddleware(async (route, from) => {
  if (import.meta.client) { return; }

  const { $pinia } = useNuxtApp();
  const global = useGlobal($pinia);
  const promoStore = usePromoStore($pinia);

  const { createLangCode } = useLocale();

  const [langFromRoute = 'en', regionCodeFromRoute = ''] = route?.name
    ? route?.name?.split('___')[1].split('-') as [Language, string]
    : [];

  const switchLocalePath = useSwitchLocalePath();

  const { data: region } = await useApi<RegionApiData>(`/api/v1/regions/region-global${regionCodeFromRoute ? `/${regionCodeFromRoute}/` : '/'}`);

  if (region.value) {
    global.SET_REGION(region.value);

    global.SET_SAMPLES({
      storageSamplePrice: region.value.storageSamplePrice,
      storageSamplePromoPrice: region.value.storageSamplePromoPrice,
      isStorageSamplePromoActive: region.value.isStorageSamplePromoActive,
      sofaSamplePrice: region.value.sofaSamplePrice,
      sofaSamplePromoPrice: region.value.sofaSamplePromoPrice,
      isSofaSamplePromoActive: region.value.isSofaSamplePromoActive
    });

    if (route.matched.length !== 0) {
      if (!langFromRoute || !region.value.availableLanguages.includes(langFromRoute) ||
    !regionCodeFromRoute || regionCodeFromRoute !== region.value.regionCode.toLowerCase()) {
        const isRoutWithDefaultLanguage = route?.name?.split('___').includes('default');

        const newLanguage = isRoutWithDefaultLanguage
          ? region.value.availableLanguages[0]
          : region.value.availableLanguages.includes(langFromRoute) ? langFromRoute : region.value.availableLanguages[0];

        const newLocaleCode = createLangCode(newLanguage, region.value.regionCode);

        if (newLocaleCode) {
          const newUrl = switchLocalePath(newLocaleCode, route);

          if (newUrl !== route.path) {
            abortNavigation();
            return navigateTo(newUrl);
          }
        }
      }
    }

    const [{ data: promotion }, { data: reviews }] = await Promise.all([
      PROMOTION(langFromRoute, region.value.regionName),
      REVIEWS_GLOBAL()
    ]);

    if (promotion.value) {
      const {
        promotionData,
        cartRibbon,
        ribbon,
        countdown,
        extraData
      } = promotion.value;

      global.SET_PROMOTION(cartRibbon, ribbon, countdown, extraData);

      if (promotionData) {
        promoStore.SET_PROMOTION_DATA({ ...promotionData, extraData });
      }
    }

    if (reviews.value) {
      global.SET_REVIEWS(reviews.value.avgScore, reviews.value.reviewsCount, reviews.value.categories);
    }
  }
});
