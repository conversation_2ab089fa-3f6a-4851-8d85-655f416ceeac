import { FURNITURE_TYPES_KEYS } from '~/consts/types';
import { GET_SHELF_TYPE } from '~/utils/types';
import { configShelfType10, SofaFinishType } from '~/consts/shelfType10';

export const COLORS_BASIC = {
  white: {
    label: 'common.material.white'
  },
  black: {
    label: 'common.material.black'
  },
  grey: {
    label: 'common.material.grey'
  },
  classicRed: {
    label: 'common.material.classic_red'
  },
  dustyPink: {
    label: 'common.material.dusty_pink'
  },
  yellow: {
    label: 'common.material.yellow'
  },
  blue: {
    label: 'common.material.blue'
  },
  oakVeneer: {
    label: 'common.material.oak_veneer',
    customRalLabel: ''
  },
  ashVeneer: {
    label: 'common.material.ash_veneer'
  },
  cottonBeige: {
    label: 'common.material.cotton_beige'
  },
  sand: {
    label: 'common.material.sand'
  },
  darkGrey: {
    label: 'common.material.dark_grey'
  },
  skyBlue: {
    label: 'common.material.sky_blue'
  },
  terracotta: {
    label: 'common.material.terracotta'
  },
  midnightBlue: {
    label: 'common.material.midnight_blue'
  },
  burgundyRed: {
    label: 'common.material.burgundy_red'
  },
  matteBlack: {
    label: 'common.material.matte_black'
  },
  reisingersPink: {
    label: 'common.material.reisingers_pink'
  },
  sageGreen: {
    label: 'common.material.sage_green'
  },
  stoneGrey: {
    label: 'common.material.stone_grey'
  },
  walnutVeneer: {
    label: 'common.material.walnut_veneer'
  },
  clayBrown: {
    label: 'common.material.clay_brown'
  },
  oliveGreen: {
    label: 'common.material.olive_green'
  },
  antiquePink: {
    label: 'common.material.antique_pink'
  },
  mistyBlue: {
    label: 'common.material.misty_blue'
  },
  cashmereBeige: {
    label: 'common.material.cashmere_beige'
  },
  graphiteGrey: {
    label: 'common.material.graphite_grey'
  },
  cleafLight: {
    label: 'common.material.cleaf_light'
  },
  cleafDark: {
    label: 'common.material.cleaf_dark'
  },
  offWhite: {
    label: 'common.material.off_white'
  },
  oysterBeige: {
    label: 'common.material.oyster_beige'
  },
  pistachioGreen: {
    label: 'common.material.pistachio_green'
  },
  inkyBlack: {
    label: 'common.material.inky_black'
  },
  powderPink: {
    label: 'common.material.powder_pink'
  },
  mossGreen: {
    label: 'common.material.moss_green'
  }
} as const;

export interface Color {
  name: string;
  nameKey: string | string[];
  key?: keyof typeof COLORS_BASIC | Array<keyof typeof COLORS_BASIC>
  colorIndex: number;
  cv: number;
  materialId: number;
  order: number;
  iconPath: string;
  materialName: string;
  exteriorParentId?: string;
  interiorIconPath?: string;
  mixed?: boolean;
  isSolid?: boolean;
  isPlywood?: boolean;
  isWooden?: boolean;
  shelfType: SHELF_TYPE;
}

export type TypeColors = {
  [key: string]: Color;
}

export type Colors = {
  [key in FURNITURE_TYPES_KEYS]: TypeColors
}

export const COLORS = (isCorduroyAvailable: boolean = true): Colors => ({
  [FURNITURE_TYPES_KEYS.t01p]: {
    '01p-white': {
      name: '01p-white',
      nameKey: 'common.material.white_plywood',
      key: 'white',
      colorIndex: 0,
      cv: 0,
      materialId: 0,
      order: 1,
      shelfType: 0,
      iconPath: 't01p/white',
      materialName: 'white'
    },
    '01p-grey': {
      name: '01p-grey',
      nameKey: 'common.material.grey_plywood',
      key: 'grey',
      colorIndex: 2,
      cv: 3,
      materialId: 3,
      order: 2,
      shelfType: 0,
      iconPath: 't01p/grey',
      materialName: 'grey'
    },
    '01p-dusty-pink': {
      name: '01p-dusty-pink',
      nameKey: 'common.material.dusty_pink_plywood',
      key: 'dustyPink',
      colorIndex: 5,
      cv: 8,
      materialId: 8,
      order: 4,
      shelfType: 0,
      iconPath: 't01p/dusty-pink',
      materialName: 'dusty_pink'
    },
    '01p-yellow': {
      name: '01p-yellow',
      nameKey: 'common.material.yellow_plywood',
      key: 'yellow',
      colorIndex: 4,
      cv: 7,
      materialId: 7,
      shelfType: 0,
      order: 5,
      iconPath: 't01p/yellow',
      materialName: 'yellow'
    },
    '01p-black': {
      name: '01p-black',
      nameKey: 'common.material.black_plywood',
      key: 'black',
      colorIndex: 1,
      cv: 1,
      materialId: 1,
      order: 3,
      shelfType: 0,
      iconPath: 't01p/black',
      materialName: 'black'
    },
    '01p-blue': {
      name: '01p-blue',
      nameKey: 'common.material.blue_plywood',
      key: 'blue',
      colorIndex: 3,
      cv: 9,
      materialId: 9,
      order: 8,
      shelfType: 0,
      iconPath: 't01p/blue',
      materialName: 'blue'
    },
    '01p-moss-green': {
      name: '01p-moss-green',
      nameKey: 'common.material.moss_green',
      key: 'mossGreen',
      colorIndex: 11,
      cv: 11,
      materialId: 11,
      order: 1,
      shelfType: 0,
      iconPath: 't01p/moss_green',
      materialName: 'moss_green'
    }
  },
  [FURNITURE_TYPES_KEYS.t01v]: {
    '01v-ash': {
      name: '01v-ash',
      nameKey: 'common.material.ash_veneer',
      key: 'ashVeneer',
      colorIndex: 0,
      cv: 0,
      materialId: 0,
      order: 1,
      shelfType: 2,
      iconPath: 't01v/ash',
      materialName: 'ash_veneer'
    },
    '01v-oak': {
      name: '01v-oak',
      nameKey: 'common.material.oak_veneer',
      key: 'oakVeneer',
      colorIndex: 1,
      cv: 1,
      materialId: 1,
      order: 2,
      shelfType: 2,
      iconPath: 't01v/oak',
      materialName: 'oak_veneer'
    },
    '01v-walnut': {
      name: '01v-walnut',
      nameKey: 'common.material.walnut_veneer',
      key: 'walnutVeneer',
      colorIndex: 2,
      cv: 2,
      materialId: 2,
      order: 3,
      shelfType: 2,
      iconPath: 't01v/walnut',
      materialName: 'walnut_veneer'
    }
  },
  [FURNITURE_TYPES_KEYS.t02]: {
    '02-white': {
      name: '02-white',
      nameKey: 'common.material.white',
      key: 'white',
      colorIndex: 0,
      cv: 0,
      materialId: 0,
      order: 1,
      shelfType: 1,
      iconPath: 't02/white',
      materialName: 'basic_white'
    },
    '02-cotton': {
      name: '02-cotton',
      nameKey: 'common.material.cotton_beige',
      key: 'cottonBeige',
      colorIndex: 8,
      cv: 9,
      materialId: 8,
      order: 2,
      shelfType: 1,
      iconPath: 't02/cotton',
      materialName: 'cotton'
    },
    '02-sand': {
      name: '02-sand',
      nameKey: 'common.material.beige',
      key: ['sand', 'midnightBlue'],
      colorIndex: 3,
      cv: 3,
      materialId: 3,
      order: 6,
      shelfType: 1,
      iconPath: 't02/beige',
      materialName: 'beige',
      mixed: true
    },
    '02-grey': {
      name: '02-grey',
      nameKey: 'common.material.greyt02',
      key: 'grey',
      colorIndex: 9,
      cv: 10,
      materialId: 10,
      order: 3,
      shelfType: 1,
      iconPath: 't02/grey',
      materialName: 't02_grey'
    },
    '02-sky-blue': {
      name: '02-sky-blue',
      nameKey: 'common.material.sky_blue',
      key: 'skyBlue',
      colorIndex: 6,
      cv: 7,
      materialId: 8,
      order: 12,
      shelfType: 1,
      iconPath: 't02/sky-blue',
      materialName: 'sky_blue'
    },
    '02-terracotta': {
      name: '02-terracotta',
      nameKey: 'common.material.terracotta',
      key: 'terracotta',
      colorIndex: 1,
      cv: 1,
      materialId: 1,
      order: 9,
      shelfType: 1,
      iconPath: 't02/terracotta',
      materialName: 'orange'
    },
    '02-midnight-blue': {
      name: '02-midnight-blue',
      nameKey: 'common.material.indygo',
      key: 'midnightBlue',
      colorIndex: 2,
      cv: 2,
      materialId: 1,
      order: 13,
      shelfType: 1,
      iconPath: 't02/indygo',
      materialName: 'indygo'
    },
    '02-burgundy': {
      name: '02-burgundy',
      nameKey: 'common.material.burgundy_red',
      key: 'burgundyRed',
      colorIndex: 7,
      cv: 8,
      materialId: 8,
      order: 10,
      shelfType: 1,
      iconPath: 't02/burgundy',
      materialName: 'burgundy'
    },
    '02-matte-black': {
      name: '02-matte-black',
      nameKey: 'common.material.matte_black',
      key: 'matteBlack',
      colorIndex: 5,
      cv: 6,
      materialId: 6,
      order: 15,
      shelfType: 1,
      iconPath: 't02/matte-black',
      materialName: 'matte_black'
    },
    '02-reisingers-pink': {
      name: '02-reisingers-pink',
      nameKey: 'common.material.reisingers_pink',
      key: 'reisingersPink',
      colorIndex: 12,
      cv: 15,
      materialId: 15,
      order: 14,
      shelfType: 1,
      iconPath: 't02/reisingers-pink',
      materialName: 'reisingers_pink'
    },
    '02-sage-green': {
      name: '02-sage-green',
      nameKey: 'common.material.sage_green',
      key: ['stoneGrey', 'sageGreen'],
      colorIndex: 13,
      cv: 16,
      materialId: 16,
      order: 11,
      shelfType: 1,
      iconPath: 't02/sage-green',
      materialName: 'sage_green'
    },
    '02-stone-grey': {
      name: '02-stone-grey',
      nameKey: 'common.material.stone_grey',
      key: 'stoneGrey',
      colorIndex: 14,
      cv: 17,
      materialId: 17,
      order: 4,
      shelfType: 1,
      iconPath: 't02/stone-grey',
      materialName: 'stone_grey'
    }
  },
  [FURNITURE_TYPES_KEYS.t03]: {
    '03-white': {
      name: '03-white',
      nameKey: 'common.material.white',
      key: 'white',
      colorIndex: 0,
      cv: 0,
      materialId: 0,
      order: 1,
      shelfType: 3,
      iconPath: 't03_2023-05-24/white',
      materialName: 'white'
    },
    '03-white+antique-pink': {
      name: '03-white+antique-pink',
      nameKey: ['common.material.white', 'common.material.antique_pink'],
      key: ['white', 'antiquePink'],
      colorIndex: 0,
      cv: 4,
      materialId: 4,
      order: 2,
      shelfType: 3,
      iconPath: 't03_2023-05-24/white+antique-pink',
      materialName: 'white-antique-pink',
      exteriorParentId: '03-white',
      interiorIconPath: 't03_2023-05-24/interior/antique-pink'
    },
    '03-white+stone-grey': {
      name: '03-white+stone-grey',
      nameKey: ['common.material.white', 'common.material.stone_grey'],
      key: ['white', 'stoneGrey'],
      colorIndex: 0,
      cv: 15,
      materialId: 15,
      order: 3,
      shelfType: 3,
      iconPath: 't03_2023-05-24/white+stone-grey',
      materialName: 'white-stone-grey',
      exteriorParentId: '03-white',
      interiorIconPath: 't03_2023-05-24/interior/stone-grey'
    },
    '03-white+sage-green': {
      name: '03-white+sage-green',
      nameKey: ['common.material.white', 'common.material.sage_green'],
      key: ['white', 'sageGreen'],
      colorIndex: 0,
      cv: 16,
      materialId: 16,
      order: 4,
      shelfType: 3,
      iconPath: 't03_2023-05-24/white+sage-green',
      materialName: 'white-sage-green',
      exteriorParentId: '03-white',
      interiorIconPath: 't03_2023-05-24/interior/sage-green'
    },
    '03-white+misty-blue': {
      name: '03-white+misty-blue',
      nameKey: ['common.material.white', 'common.material.misty_blue'],
      key: ['white', 'mistyBlue'],
      colorIndex: 0,
      cv: 17,
      materialId: 17,
      order: 5,
      shelfType: 3,
      iconPath: 't03_2023-05-24/white+misty-blue',
      materialName: 'white-misty-blue',
      exteriorParentId: '03-white',
      interiorIconPath: 't03_2023-05-24/interior/misty-blue'
    },

    '03-cashmere-beige': {
      name: '03-cashmere-beige',
      nameKey: 'common.material.cashmere_beige',
      key: 'cashmereBeige',
      colorIndex: 1,
      cv: 1,
      materialId: 1,
      order: 6,
      shelfType: 3,
      iconPath: 't03_2023-05-24/cashmere-beige',
      materialName: 'cashmere'
    },
    '03-cashmere-beige+antique-pink': {
      name: '03-cashmere-beige+antique-pink',
      nameKey: ['common.material.cashmere_beige', 'common.material.antique_pink'],
      key: ['cashmereBeige', 'antiquePink'],
      colorIndex: 3,
      cv: 3,
      materialId: 3,
      order: 7,
      shelfType: 3,
      iconPath: 't03_2023-05-24/cashmere-beige+antique-pink',
      materialName: 'cashmere-antique-pink',
      exteriorParentId: '03-cashmere-beige',
      interiorIconPath: 't03_2023-05-24/interior/antique-pink'
    },
    '03-cashmere-beige+stone-grey': {
      name: '03-cashmere-beige+stone-grey',
      nameKey: ['common.material.cashmere_beige', 'common.material.stone_grey'],
      key: ['cashmereBeige', 'stoneGrey'],
      colorIndex: 5,
      cv: 18,
      materialId: 18,
      order: 8,
      shelfType: 3,
      iconPath: 't03_2023-05-24/cashmere-beige+stone-grey',
      materialName: 'cashmere-stone-grey',
      exteriorParentId: '03-cashmere-beige',
      interiorIconPath: 't03_2023-05-24/interior/stone-grey'
    },
    '03-cashmere-beige+sage-green': {
      name: '03-cashmere-beige+sage-green',
      nameKey: ['common.material.cashmere_beige', 'common.material.sage_green'],
      key: ['cashmereBeige', 'sageGreen'],
      colorIndex: 2,
      cv: 19,
      materialId: 19,
      order: 9,
      shelfType: 3,
      iconPath: 't03_2023-05-24/cashmere-beige+sage-green',
      materialName: 'cashmere-sage-green',
      exteriorParentId: '03-cashmere-beige',
      interiorIconPath: 't03_2023-05-24/interior/sage-green'
    },
    '03-cashmere-beige+misty-blue': {
      name: '03-cashmere-beige+misty-blue',
      nameKey: ['common.material.cashmere_beige', 'common.material.misty_blue'],
      key: ['cashmereBeige', 'mistyBlue'],
      colorIndex: 2,
      cv: 20,
      materialId: 20,
      order: 10,
      shelfType: 3,
      iconPath: 't03_2023-05-24/cashmere-beige+misty-blue',
      materialName: 'cashmere-misty-blue',
      exteriorParentId: '03-cashmere-beige',
      interiorIconPath: 't03_2023-05-24/interior/misty-blue'
    },
    '03-misty-blue': {
      name: '03-misty-blue',
      nameKey: 'common.material.misty_blue',
      key: 'mistyBlue',
      colorIndex: 0,
      cv: 9,
      materialId: 0,
      order: 0,
      iconPath: 't03/misty-blue',
      materialName: 'misty-blue',
      shelfType: 3
    },
    '03-graphite-grey': {
      name: '03-graphite-grey',
      nameKey: 'common.material.graphite_grey',
      key: 'graphiteGrey',
      colorIndex: 2,
      cv: 2,
      materialId: 2,
      order: 11,
      shelfType: 3,
      iconPath: 't03_2023-05-24/graphite-grey',
      materialName: 'graphite-grey'
    },
    '03-graphite-grey+antique-pink': {
      name: '03-graphite-grey+antique-pink',
      nameKey: ['common.material.graphite_grey', 'common.material.antique_pink'],
      key: ['graphiteGrey', 'antiquePink'],
      colorIndex: 2,
      cv: 6,
      materialId: 6,
      order: 12,
      shelfType: 3,
      iconPath: 't03_2023-05-24/graphite-grey+antique-pink',
      materialName: 'graphite-grey-antique-pink',
      exteriorParentId: '03-graphite-grey',
      interiorIconPath: 't03_2023-05-24/interior/antique-pink'
    },
    '03-graphite-grey+stone-grey': {
      name: '03-graphite-grey+stone-grey',
      nameKey: ['common.material.graphite_grey', 'common.material.stone_grey'],
      key: ['graphiteGrey', 'stoneGrey'],
      colorIndex: 2,
      cv: 12,
      materialId: 12,
      order: 13,
      shelfType: 3,
      iconPath: 't03_2023-05-24/graphite-grey+stone-grey',
      materialName: 'graphite-grey-stone-grey',
      exteriorParentId: '03-graphite-grey',
      interiorIconPath: 't03_2023-05-24/interior/stone-grey'
    },
    '03-graphite-grey+sage-green': {
      name: '03-graphite-grey+sage-green',
      nameKey: ['common.material.graphite_grey', 'common.material.sage_green'],
      key: ['graphiteGrey', 'sageGreen'],
      colorIndex: 2,
      cv: 13,
      materialId: 13,
      order: 14,
      shelfType: 3,
      iconPath: 't03_2023-05-24/graphite-grey+sage-green',
      materialName: 'graphite-grey-sage-green',
      exteriorParentId: '03-graphite-grey',
      interiorIconPath: 't03_2023-05-24/interior/sage-green'
    },
    '03-graphite-grey+misty-blue': {
      name: '03-graphite-grey+misty-blue',
      nameKey: ['common.material.graphite_grey', 'common.material.misty_blue'],
      key: ['graphiteGrey', 'mistyBlue'],
      colorIndex: 2,
      cv: 14,
      materialId: 14,
      order: 15,
      shelfType: 3,
      iconPath: 't03_2023-05-24/graphite-grey+misty-blue',
      materialName: 'graphite-grey-misty-blue',
      exteriorParentId: '03-graphite-grey',
      interiorIconPath: 't03_2023-05-24/interior/misty-blue'
    }
  },
  [FURNITURE_TYPES_KEYS.t13]: {
    '13-white+plywood': {
      name: '13-white+plywood',
      nameKey: 'common.material.white_plywood',
      key: 'white',
      colorIndex: 5,
      cv: 5,
      materialId: 5,
      order: 7,
      shelfType: 4,
      iconPath: 't13/white-plywood',
      materialName: 'white-plywood',
      mixed: true,
      isPlywood: true
    },
    '13-grey+plywood': {
      name: '13-grey+plywood',
      nameKey: 'common.material.grey_plywood',
      key: 'grey',
      colorIndex: 6,
      cv: 6,
      materialId: 6,
      order: 8,
      shelfType: 4,
      iconPath: 't13/grey-plywood',
      materialName: 'grey-plywood',
      mixed: true,
      isPlywood: true
    },
    '13-white': {
      name: '13-white',
      nameKey: 'common.material.white',
      key: 'white',
      colorIndex: 0,
      cv: 0,
      materialId: 0,
      order: 1,
      shelfType: 4,
      iconPath: 't13/white',
      materialName: 'white',
      mixed: false,
      isSolid: true
    },
    '13-grey': {
      name: '13-grey',
      nameKey: 'common.material.grey',
      key: 'grey',
      colorIndex: 3,
      cv: 3,
      materialId: 3,
      order: 2,
      shelfType: 4,
      iconPath: 't13/grey',
      materialName: 'grey',
      mixed: false,
      isSolid: true
    },
    '13-black': {
      name: '13-black',
      nameKey: 'common.material.black',
      key: 'black',
      colorIndex: 11,
      cv: 11,
      materialId: 11,
      order: 3,
      shelfType: 4,
      iconPath: 't13/black',
      materialName: 'black',
      mixed: false,
      isSolid: true
    },
    '13-clay-brown': {
      name: '13-clay-brown',
      nameKey: 'common.material.clay_brown',
      key: 'clayBrown',
      colorIndex: 8,
      cv: 8,
      materialId: 8,
      order: 4,
      shelfType: 4,
      iconPath: 't13/clay-brown',
      materialName: 'clay-brown',
      mixed: false,
      isSolid: true
    },
    '13-olive-green': {
      name: '13-olive-green',
      nameKey: 'common.material.olive_green',
      key: 'oliveGreen',
      colorIndex: 9,
      cv: 9,
      materialId: 9,
      order: 5,
      shelfType: 4,
      iconPath: 't13/olive-green',
      materialName: 'olive-green',
      mixed: false,
      isSolid: true
    },
    '13-sand': {
      name: '13-sand',
      nameKey: 'common.material.sand',
      key: 'sand',
      colorIndex: 10,
      cv: 10,
      materialId: 10,
      order: 6,
      shelfType: 4,
      iconPath: 't13/sand',
      materialName: 'sand',
      mixed: false,
      isSolid: true
    }
  },
  [FURNITURE_TYPES_KEYS.t13v]: {
    '13-cleaf-light': {
      name: '13-cleaf-light',
      nameKey: 'common.material.cleaf_light',
      key: 'cleafLight',
      colorIndex: 0,
      cv: 0,
      materialId: 0,
      order: 0,
      iconPath: 't13/cleaf-light',
      materialName: 'cleaf-light',
      mixed: false,
      isWooden: true,
      shelfType: 5
    },
    '13-cleaf-dark': {
      name: '13-cleaf-dark',
      nameKey: 'common.material.cleaf_dark',
      key: 'cleafDark',
      colorIndex: 1,
      cv: 1,
      materialId: 1,
      order: 1,
      iconPath: 't13/cleaf-dark',
      materialName: 'cleaf-dark',
      mixed: false,
      isWooden: true,
      shelfType: 5
    }
  },
  [FURNITURE_TYPES_KEYS.t23]: {
    '23-offwhite': {
      name: '23-offwhite',
      nameKey: 'common.material.off_white',
      key: 'offWhite',
      colorIndex: 0,
      cv: 0,
      materialId: 0,
      order: 0,
      shelfType: 6,
      iconPath: 't23/off-white',
      materialName: 'off-white'
    },
    '23-oyster-beige': {
      name: '23-oyster-beige',
      nameKey: 'common.material.oyster_beige',
      key: 'oysterBeige',
      colorIndex: 1,
      cv: 1,
      materialId: 1,
      order: 1,
      shelfType: 6,
      iconPath: 't23/oyster-beige',
      materialName: 'oyster-beige'
    },
    '23-pistachio-green': {
      name: '23-pistachio-green',
      nameKey: 'common.material.pistachio_green',
      key: 'pistachioGreen',
      colorIndex: 2,
      cv: 2,
      materialId: 2,
      order: 2,
      shelfType: 6,
      iconPath: 't23/pistachio-green',
      materialName: 'pistachio-green'
    },
    '23-inky-black': {
      name: '23-inky-black',
      nameKey: 'common.material.inky_black',
      key: 'inkyBlack',
      colorIndex: 3,
      cv: 3,
      materialId: 3,
      order: 3,
      shelfType: 6,
      iconPath: 't23/inky-black',
      materialName: 'inky-black'
    },
    '23-powder-pink': {
      name: '23-powder-pink',
      nameKey: 'common.material.powder_pink',
      key: 'powderPink',
      colorIndex: 4,
      cv: 4,
      materialId: 4,
      order: 4,
      shelfType: 6,
      iconPath: 't23/powder-pink',
      materialName: 'powder-pink'
    }
  },
  [FURNITURE_TYPES_KEYS.t24]: {
    '24-offwhite': {
      name: '24-offwhite',
      nameKey: 'common.material.off_white',
      key: 'offWhite',
      colorIndex: 0,
      cv: 0,
      materialId: 0,
      order: 0,
      shelfType: 7,
      iconPath: 't24/off-white',
      materialName: 'off-white'
    },
    '24-oyster-beige': {
      name: '24-oyster-beige',
      nameKey: 'common.material.oyster_beige',
      key: 'oysterBeige',
      colorIndex: 1,
      cv: 1,
      materialId: 1,
      order: 1,
      shelfType: 7,
      iconPath: 't24/oyster-beige',
      materialName: 'oyster-beige'
    },
    '24-pistachio-green': {
      name: '24-pistachio-green',
      nameKey: 'common.material.pistachio_green',
      key: 'pistachioGreen',
      colorIndex: 2,
      cv: 2,
      materialId: 2,
      order: 2,
      shelfType: 7,
      iconPath: 't24/pistachio-green',
      materialName: 'pistachio-green'
    },
    '24-inky-black': {
      name: '24-inky-black',
      nameKey: 'common.material.inky_black',
      key: 'inkyBlack',
      colorIndex: 3,
      cv: 3,
      materialId: 3,
      order: 3,
      shelfType: 7,
      iconPath: 't24/inky-black',
      materialName: 'inky-black'
    },
    '24-powder-pink': {
      name: '24-powder-pink',
      nameKey: 'common.material.powder_pink',
      key: 'powderPink',
      colorIndex: 4,
      cv: 4,
      materialId: 4,
      order: 4,
      shelfType: 7,
      iconPath: 't24/powder-pink',
      materialName: 'powder-pink'
    }
  },
  [FURNITURE_TYPES_KEYS.t25]: {
    '25-offwhite': {
      name: '25-offwhite',
      nameKey: 'common.material.off_white',
      key: 'offWhite',
      colorIndex: 0,
      cv: 0,
      materialId: 0,
      order: 0,
      shelfType: 8,
      iconPath: 't25/off-white',
      materialName: 'off-white'
    },
    '25-oyster-beige': {
      name: '25-oyster-beige',
      nameKey: 'common.material.oyster_beige',
      key: 'oysterBeige',
      colorIndex: 1,
      cv: 1,
      materialId: 1,
      order: 1,
      shelfType: 8,
      iconPath: 't25/oyster-beige',
      materialName: 'oyster-beige'
    },
    '25-pistachio-green': {
      name: '25-pistachio-green',
      nameKey: 'common.material.pistachio_green',
      key: 'pistachioGreen',
      colorIndex: 2,
      cv: 2,
      materialId: 2,
      order: 2,
      shelfType: 8,
      iconPath: 't25/pistachio-green',
      materialName: 'pistachio-green'
    },
    '25-inky-black': {
      name: '25-inky-black',
      nameKey: 'common.material.inky_black',
      key: 'inkyBlack',
      colorIndex: 3,
      cv: 3,
      materialId: 3,
      order: 3,
      shelfType: 8,
      iconPath: 't25/inky-black',
      materialName: 'inky-black'
    },
    '25-powder-pink': {
      name: '25-powder-pink',
      nameKey: 'common.material.powder_pink',
      key: 'powderPink',
      colorIndex: 4,
      cv: 4,
      materialId: 4,
      order: 4,
      shelfType: 8,
      iconPath: 't25/powder-pink',
      materialName: 'powder-pink'
    }
  },
  [FURNITURE_TYPES_KEYS.s01]: configShelfType10.filter(color => !isCorduroyAvailable ? color.finish !== SofaFinishType.CORDUROY : true)
    .reduce((acc :{[key: string]: Color}, item, index) => {
      if (item.value !== -1) {
        acc[item.material_name] = {
          name: `10-${item.material_name}`,
          nameKey: item.translation,
          key: item.material_name,
          colorIndex: item.value,
          cv: item.value,
          materialId: item.value,
          order: index,
          shelfType: 10,
          iconPath: item.swatchPath,
          materialName: item.material_name
        };
      }

      return acc;
    }, {})
});

export const interiorCOLORS = (): Partial<Colors> => ({
  [FURNITURE_TYPES_KEYS.t03]: {
    '03-misty-blue': {
      name: '03-misty-blue',
      nameKey: 'common.material.misty_blue',
      key: 'mistyBlue',
      colorIndex: 0,
      cv: 9,
      materialId: 0,
      order: 0,
      iconPath: 't03/misty-blue',
      materialName: 'misty-blue',
      shelfType: 3
    },
    '03-sage-green': {
      name: '03-sage-green',
      nameKey: 'common.material.sage_green',
      key: 'sageGreen',
      colorIndex: 0,
      cv: 0,
      materialId: 0,
      order: 0,
      iconPath: 't03/sage-green',
      materialName: 'sage-green',
      shelfType: 3
    },
    '03-stone-grey': {
      name: '03-stone-grey',
      nameKey: 'common.material.stone_grey',
      key: 'stoneGrey',
      colorIndex: 0,
      cv: 0,
      materialId: 0,
      order: 0,
      iconPath: 't03/stone-grey',
      materialName: 'stone-grey',
      shelfType: 3
    },
    '03-antique-pink': {
      name: '03-antique-pink',
      nameKey: 'common.material.antique_pink',
      key: 'antiquePink',
      colorIndex: 0,
      cv: 0,
      materialId: 0,
      order: 0,
      iconPath: 't03/antique-pink',
      materialName: 'antique-pink',
      shelfType: 3
    }
  }
});

export const GET_COLOR_OBJECT = (furnitureType: SHELF_TYPE, cv: number, materialName?: string): Color => {
  const type = GET_SHELF_TYPE(furnitureType);
  const colors = COLORS();
  let colorObject = null;

  if (materialName) {
    [[, colorObject]] = Object.entries(colors[type]).filter(item => item[1].materialName === materialName);
  } else {
    [[, colorObject]] = Object.entries(colors[type]).filter((item) => {
      return item[1].cv === cv;
    });
  }

  return colorObject;
};
