<template>
  <main class="bg-lime">
    <Head>
      <Title>{{ $t('lp.influencers.title') }}</Title>
      <Meta
        name="og:title"
        hid="og:title"
        v-bind:content="$t('lp.influencers.title')"
      />
      <Meta
        name="description"
        hid="description"
        v-bind:content="$t('lp.influencers.description')"
      />
      <Meta
        name="og:description"
        hid="og:description"
        v-bind:content="$t('lp.influencers.description')"
      />
      <Meta
        name="og:image"
        hid="og:image"
        v-bind:content="$t('lp.influencers.title')"
      />
    </Head>

    <section class="grid-container pt-48 text-offblack-900">
      <div class="flex justify-start items-center md-max:hidden mb-40">
        <BaseLink
          v-bind="{
            variant: 'custom',
            href: $addLocaleToPath('homepage'),
            trackData: {}
          }"
          class="normal-14 text-neutral-900 after:content-[''] relative after:absolute after:-right-[18px] after:top-1/2 after:-translate-y-1/2 after:w-4 after:h-4 after:bg-neutral-900"
        >
          {{ $t('menu.labels.home') }}
        </BaseLink>
        <p class="ml-28 normal-14 text-neutral-700">
          {{ $t('menu.labels.influencers') }}
        </p>
      </div>
      <div class="md:hidden mb-24">
        <BaseLink
          v-bind="{
            variant: 'custom',
            href: $addLocaleToPath('homepage'),
            trackData: {}
          }"
          class="semibold-14 text-neutral-900 flex justify-start items-center"
        >
          <IconArrowRight class="ty-icon--s text-neutral-900 rotate-180 -mt-[2px] mr-8" />
          {{ $t('menu.labels.back_to_home') }}
        </BaseLink>
      </div>
      <h1 class="semibold-54">
        {{ $t('lp.influencers.title') }}
      </h1>
      <h2 class="semibold-20 lg:semibold-18 mt-16 md:mt-8">
        {{ $t('lp.influencers.description') }}
      </h2>
    </section>

    <section class="mt-32 md:mt-24 overflow-x-hidden text-offblack-900">
      <div class="grid-container">
        <LazyBaseCarousel
          hydrate-on-visible
          class="flex justify-between "
          v-bind="{
            options: {
              centeredSlidesBounds: true,
              slidesPerView: 'auto',
              freeMode: true,
              breakpoints: 0,
              on: {
                afterInit: (swiper) => {
                  swiper.params.centeredSlides = true
                  const activeCategoryIndex = activeAvatarName ? influencersData.findIndex(influencer => influencer.name === activeAvatarName) : 0
                  swiper.slideTo(activeCategoryIndex, activeCategoryIndex * 50);
                },
              }
            },
            name: 'avatarCarousel',
            isNavigation: false,
            swiperRootClasses: '!overflow-visible w-full',
          }"
        >
          <BaseCarouselSlide
            v-for="(avatar, index) in influencersData"
            :key="`${avatar.name}_${index}`"
            class="max-w-96 lg:max-w-128 mr-16 lg:mr-48 last:mr-0"
          >
            <BaseLink
              as-nuxt-link
              variant="custom"
              class="flex flex-col items-center gap-12 cursor-pointer relative transition-opacity basic-transition"
              v-bind="{
                trackData: {},
                class: [
                  { 'opacity-20 pointer-events-none': isClearAllActive && !intersection(avatar.furniture.map((furniture) => furniture.hashtags).flat(), activeHashTags).length
                    || (activeAvatarName && avatar.name !== activeAvatarName) },
                ],
                to: {
                  path: $route.path,
                  query: {
                    ...(!activeAvatarName && { influencer: avatar.name }),
                    ...($route.query?.tags && { tags: $route.query.tags.split(',').filter(value => avatar.furniture.find(furniture => furniture.hashtags.includes(value))).join(',') }),
                  },
                },
              }"
            >
              <BasePicture
                class="w-96 h-96 lg:w-128 lg:h-128 rounded-full overflow-hidden object-cover"
                img-classes="w-full aspect-square object-cover"
                type="M D"
                v-bind="{
                  isRetinaUploaded: false,
                  alt: avatar.name,
                  path: `lp/influencers/${avatar.folderName}/avatar`,
                }"
              />

              <p class="normal-14 lg:normal-16 text-center select-none">
                {{ avatar.name }}
              </p>
              <div
                v-if="activeAvatarName === avatar.name"
                class="absolute top-0 right-0 -translate-1/2 flex justify-center items-center rounded-full bg-orange border-white border text-white p-[6px] lg:p-8"
              >
                <IconClose class="w-16 h-16 lg:w-20 lg:h-20" />
              </div>
            </BaseLink>
          </BaseCarouselSlide>
        </LazyBaseCarousel>
      </div>
    </section>

    <section class="grid-container mt-32 md:mt-64">
      <div class="flex flex-wrap items-center gap-12">
        <BaseLink
          v-for="(tag, index) in filtersState.hashTags"
          as-nuxt-link
          variant="custom"
          v-bind="{
            trackData: {},
            class: [
              { 'md-max:hidden': index > 10 && !isExpanded },
              { 'md:hidden': index > 15 && !isExpanded }
            ],
            key: `${tag.hash}_${index}`,
            to: {
              path: $route.path,
              query: removeEmptyProperties({
                ...($route.query?.influencer && { influencer: $route.query?.influencer }),
                tags: ($route.query?.tags?.split(',') || []).includes(tag.hash) ?
                  $route.query?.tags?.split(',').filter(value => value !== tag.hash).join(',') :
                  [...($route.query?.tags?.split(',') || []), tag.hash].filter(value => !isNil(value)).join(',')
              }),
            },
          }"
        >
          <BasePill
            v-model="tag.isActive"
            variant="filter"
          >
            #{{ tag.hash }}
          </BasePill>
        </BaseLink>
        <div
          class="flex items-center ty-link-s"
          v-bind="{
            class: [
              { 'md-max:hidden': filtersState.hashTags.length < 10 },
              { 'md:hidden': filtersState.hashTags.length < 15 },
              { 'hidden': isExpanded }
            ],
          }"
          v-on:click="isExpanded = true"
        >
          <IconPlus class="inline-block w-20 h-20" />
          <span class="link--underline-animated semi-bold-14">
            {{ $t('lp.tylkoforbusiness.form.showMore') }}
          </span>
        </div>

        <BaseLink
          v-if="$route.query?.tags"
          as-nuxt-link
          class="flex items-center ty-link-s"
          variant="custom"
          v-bind="{
            to: {
              trackData: {},
              path: $route.path,
              query: removeEmptyProperties({
                ...($route.query?.influencer && { influencer: $route.query?.influencer }),
              })
            }
          }"
        >
          <IconClose class="inline-block w-20 h-20" />
          <span class="link--underline-animated semi-bold-14">
            {{ $t('plp.filters.header.clear_filters') }}
          </span>
        </BaseLink>
      </div>
    </section>

    <article>
      <template
        v-for="(furniture, index) in activeProducts"
        :key="`${furniture.id}_${index}`"
      >
        <figure
          v-if="index <= productMaxCount"
          class="md:grid md:grid-cols-2 md:gap-24 lg:gap-y-0 xl:gap-x-48 mt-64 lg:mt-80 first:mt-32 relative lg:grid-rows-[auto_1fr] items-start"
        >
          <div class="w-full max-w-[792px] md:ml-auto md:pl-32 lg:pl-48 xl:pl-56 lg:row-span-2">
            <LazyBaseCarousel
              hydrate-on-visible
              class="influencer-carousel group relative md:rounded-12 md:overflow-hidden"
              v-bind="{
                options: {
                  slidesPerView: 1,
                  breakpoints: 0,
                  navigation: {
                    lockClass: 'hidden',
                    disabledClass: 'swiper-button-off',
                    nextEl: `#nextp-${index}`,
                    prevEl: `#prevp-${index}`,
                  },
                },
                name: `product_${index}`,
                isNavigation: false,
                isPaginationDesktop: furniture.images > 1,
                isPaginationMobile: furniture.images > 1,
              }"
            >
              <template
                v-if="furniture.images > 1"
                #pagination
              >
                <div class="hidden lg:block opacity-0 group-hover:opacity-100 transition-opacity basic-transition">
                  <button
                    v-bind="{
                      id:`prevp-${index}`
                    }"
                    disabled
                    class="rounded-full bg-white p-8
                              absolute top-1/2 left-24 -translate-y-1/2
                              [&:disabled]:opacity-40 transition-opacity basic-transition"
                  >
                    <IconCaretLeft class="w-24 h-24" />
                  </button>
                  <button
                    v-bind="{
                      id:`nextp-${index}`
                    }"
                    disabled
                    class="rounded-full bg-white p-8
                              absolute top-1/2 right-24 -translate-y-1/2
                              [&:disabled]:opacity-40 transition-opacity basic-transition"
                  >
                    <IconCaretRight class="w-24 h-24" />
                  </button>
                </div>
              </template>
              <BaseCarouselSlide
                v-for="(image, i) in furniture.images"
                :key="`product_${i}`"
                class="w-full"
              >
                <BasePicture
                  class="w-full aspect-square"
                  img-classes="w-full h-full object-cover aspect-square"
                  type="M D"
                  v-bind="{
                    isRetinaUploaded: false,
                    alt: '',
                    path: `lp/influencers/${furniture.folderName}/${furniture.index}/${i + 1}`,
                  }"
                />
              </BaseCarouselSlide>
            </LazyBaseCarousel>
          </div>
          <figcaption class="flex gap-16 px-16 md:px-32 md:col-span-2 md:order-first lg:col-span-1 lg:order-none lg:px-0">
            <BasePicture
              class="absolute left-16 -translate-y-1/2 z-1 md:static md:translate-y-0 md:translate-x-0
                      shrink-0 w-64 h-64 lg:w-72 lg:h-72 rounded-full overflow-hidden object-cover
                      border-white border-4 md:border-2 bg-white"
              img-classes="w-full aspect-square object-cover"
              type="M D"
              v-bind="{
                isRetinaUploaded: false,
                alt: furniture.influencerName,
                path: `lp/influencers/${furniture.folderName}/avatar`,
              }"
            />

            <div class="mt-40 md:mt-0 lg:max-w-[792px] md:mr-auto md:pr-32 lg:pr-48 xl:pr-56 w-full">
              <h3 class="semibold-14">
                {{ furniture.influencerName }}
              </h3>
              <p
                class="normal-14 mt-4 line-clamp-3"
                v-html="influencersData.find(influencer => influencer.name === furniture.influencerName)?.description[locale] || ''"
              />
            </div>
          </figcaption>
          <div class="overflow-hidden">
            <div class="md-max:grid-container max-w-[792px] md:mr-auto md:pr-32 lg:pr-48 xl:pr-56 w-full">
              <div class="flex items-center gap-16 mt-32 md:mt-0 lg:mt-24 xl:mt-32">
                <h2 class="semibold-24 xl:semibold-32 flex-1">
                  {{ $t('lp.influencers.shop') }}
                </h2>
                <button
                  v-bind="{
                    id:`prev-${index}`
                  }"
                  disabled
                  class="rounded-full bg-white p-8 md-max:hidden
                        [&:disabled]:opacity-40 transition-opacity basic-transition"
                >
                  <IconCaretLeft class="w-24 h-24" />
                </button>
                <button
                  v-bind="{
                    id:`next-${index}`
                  }"
                  disabled
                  class="rounded-full bg-white p-8 md-max:hidden
                        [&:disabled]:opacity-40 transition-opacity basic-transition"
                >
                  <IconCaretRight class="w-24 h-24" />
                </button>
              </div>
              <LazyBaseCarousel
                class="flex justify-between mt-16"
                v-bind="{
                  options: {
                    slidesPerView: 'auto',
                    breakpoints: 0,
                    navigation: {
                      lockClass: 'hidden',
                      nextEl: `#next-${index}`,
                      prevEl: `#prev-${index}`,
                    },
                  },
                  name: `productsCarousel-${index}`,
                  isNavigation: false,
                  swiperRootClasses: 'w-full !overflow-visible',
                }"
              >
                <BaseCarouselSlide
                  v-for="furnitureId in furniture.related"
                  :key="`product_${furnitureId}`"
                  class="relative max-w-[70%] mr-8 last:mr-0 md:max-w-[calc(100%/2-8px/2)] xl:max-w-[calc(100%/3-32px/3)] xl:mr-16 xl:last:mr-0"
                >
                  <div
                    v-if="status === 'pending'"
                    class="w-full aspect-square bg-beige-200"
                  />
                  <a
                    v-else
                    :href="`https://tylko.com${data[furnitureId.split(',')[0]]?.url}`"
                  >
                    <img
                      class="w-full aspect-square object-cover bg-beige-200"
                      :src="data[furnitureId.split(',')[0]]?.image"
                    >
                    <p
                      v-if="data[furnitureId.split(',')[0]]?.is_new"
                      class="h-[18px] semibold-10 uppercase mt-12"
                      data-testid="product-card-label"
                      :class="bottomLabel([{ value: 'new' }])?.textColorClass || ''"
                      v-html="bottomLabel([{ value:'new' }])?.translationKey && $t(bottomLabel([{ value:'new' }]).translationKey)"
                    />
                    <p class="semibold-14 mt-12 pr-16 line-clamp-2">
                      {{ data[furnitureId.split(',')[0]]?.name }}
                    </p>
                    <p
                      v-if="data[furnitureId.split(',')[0]]?.region_price_with_discount"
                      class="semibold-14 mt-4 pr-16"
                    >
                      {{ formatPrice(data[furnitureId.split(',')[0]]?.region_price_with_discount) }}
                    </p>
                    <p class="normal-14 mt-12 pr-16">
                      {{ data[furnitureId.split(',')[0]]?.size }}
                    </p>
                    <BaseLink
                      class="mt-16 ty-btn--s"
                      variant="outlined"
                      v-bind="{
                        href: `https://tylko.com${data[furnitureId.split(',')[0]]?.url}`,
                        variant: 'outlined',
                        trackData: {
                          eventLabel: ''
                        }
                      }"
                    >
                      {{ $t('common.configure') }}
                    </BaseLink>
                  </a>
                  <BaseButton
                    v-if="status !== 'pending'"

                    variant="custom"
                    class="absolute top-4 right-4 lg:top-8 lg:right-8 rounded-full p-12 bg-white hover:bg-neutral-400 transition-all basic-transition"
                    v-bind="{
                      trackData: {}
                    }"
                    v-on:click="addToWishlist(furnitureId)"
                  >
                    <IconHeart class="w-20 h-20 lg:w-20 lg:h-20" />
                  </BaseButton>
                </BaseCarouselSlide>
              </LazyBaseCarousel>
            </div>
          </div>
        </figure>
      </template>

      <BaseButton
        v-if="activeProducts.length > productMaxCount"
        variant="outlined"
        class="mt-64 lg:mt-80 mx-auto block"
        data-testid="show-more-items-button"
        v-bind="{
          trackData: {}
        }"
        v-on:click="productMaxCount += 5"
      >
        {{ $t('lp.tylkoforbusiness.form.showMore') }}
      </BaseButton>
    </article>

    <LazyColaborationsOlapicFeed hydrate-on-visible />

    <ClientOnly>
      <LazyColaborationsModalAddToWishlist
        v-if="isSaveForLaterModalOpen"
        v-model="isSaveForLaterModalOpen"
        :hydrate-when="isSaveForLaterModalOpen"
        v-bind="{
          imageSrc: data?.[saveForLaterFurnitureId.split(',')[0]]?.image,
          furnitureId: saveForLaterFurnitureId
        }"
      />

      <LazyModalWishlist
        v-if="isWishlistModalOpen "
        v-model="isWishlistModalOpen"
        :hydrate-when="isWishlistModalOpen"
        v-bind="{
          wishlistItem
        }"
      />
    </ClientOnly>
  </main>
</template>

<script setup lang="ts">
import { intersection, isNil } from 'lodash-es';
import { useToast } from 'vue-toastification';
import useInfluencers from '~/composables/influencers/useInfluencers';
import { useApi } from '~/composables/useApi';
import { ADD_TO_WISHLIST_BY_ID } from '~/api/wishlist';
definePageMeta({ key: () => 'influencersKey', name: 'influencers' });

const { data: sanityData } = await useApi<any[]>('nuxt-api/influencersSanity');
const {
  influencersData,
  activeProducts,
  activeAvatarName,
  productMaxCount,
  activeHashTags,
  filtersState,
  jetty,
  watty
} = useInfluencers(sanityData.value!);

const isExpanded = ref(false);

const isClearAllActive = computed(() => activeHashTags.value.length);

watch([activeAvatarName, activeHashTags], () => {
  productMaxCount.value = 5;
});

function removeEmptyProperties (obj: Record<string, any>): Record<string, any> {
  return Object.fromEntries(Object.entries(obj).filter(([_, v]) => v != null && v !== ''));
}

const isSale = true;
const labelsProps = {
  soon: {
    textColorClass: 'text-[#8868AA]',
    translationKey: 'plp.board.product_card.label.soon'
  },
  new: {
    textColorClass: 'text-[#5A834B]',
    translationKey: 'plp.board.product_card.label.new_arrival'
  },
  discount: {
    textColorClass: isSale ? 'text-[#ffad01]' : 'text-orange',
    translationKey: isSale ? 'plp.board.product_card.label.season_sale' : 'plp.board.product_card.label.sale'
  },
  top_seller: {
    textColorClass: 'text-[#BE7958]',
    translationKey: 'plp.filters.features.top_seller'
  },
  special_edition: {
    textColorClass: 'text-neutral-800',
    translationKey: 'plp.filters.features.special_edition'
  },
  special: {
    textColorClass: 'text-orange',
    translationKey: 'plp.filters.features.special'
  }
} as const;

const bottomLabel = (labels:Array<{ value: string }>) => {
  const label = labels.find(
    el => el.value === 'new' ||
          el.value === 'soon' ||
          el.value === 'top_seller' ||
          el.value === 'special_edition' ||
          el.value === 'special'
  );

  return labelsProps[label?.value] || '';
};

const global = useGlobal();
const { locale } = useLocale();
const { data, status } = await useApi('/api/v1/influencers_lp/', {
  query: {
    region: global.regionName,
    lang: locale.value,
    jetty_ids: jetty,
    watty_ids: watty
  },
  transform: (data) => {
    return data.reduce((acc, curr) => {
      const { id, ...rest } = curr;
      acc[curr.id] = rest;
      return acc;
    }, {});
  }
});

const { format } = usePrice();

const formatPrice = (price: number) => format(price, global.currencyCode, global.countryLocale);
const isSaveForLaterModalOpen = ref(false);
const isWishlistModalOpen = ref(false);
const wishlistItem = ref({
  preview: null,
  title: null,
  region_price: null,
  region_price_with_discount: null
});
const saveForLaterFurnitureId = ref('');
const toast = useToast();
const i18n = useI18n();

const addToWishlist = async (id: string) => {
  if (global.isSignedIn) {
    const furniture = data.value[id.split(',')[0]];
    const [_id, model] = id.split(',');
    const models = {
      j: 'jetty',
      w: 'watty',
      s: 'sotty'
    };

    try {
      await ADD_TO_WISHLIST_BY_ID(_id, models[model]);

      isWishlistModalOpen.value = true;
      wishlistItem.value = {
        preview: furniture.image,
        title: furniture.name,
        region_price: furniture.region_price_with_discount || furniture.region_price
      };

      isWishlistModalOpen.value = true;

      setTimeout(() => {
        isWishlistModalOpen.value = false;
      }, 3000);
    } catch (error) {
      console.error(error);
      toast.error(i18n.t('common.error.connection'));
    }
  } else {
    saveForLaterFurnitureId.value = id;
    isSaveForLaterModalOpen.value = true;
  }
};
</script>

<style lang="scss" scoped>
:deep(.influencer-carousel) {
  .swiper-pagination {
    @apply bg-neutral-400/80;
    @apply flex gap-8 p-8 rounded-full;
    @apply bottom-16 left-1/2 transform -translate-x-1/2;

    position: absolute!important;

    &-bullet {
      @apply w-8 h-8 rounded-full bg-neutral-500;
    }

    &-bullet-active {
      @apply bg-neutral-900;
    }
  }
}
</style>
