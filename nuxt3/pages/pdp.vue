<template>
  <main class="relative reset-modern-navigation-page-padding">
    <ClientOnly v-if="!loader && !regionWithoutTone">
      <Teleport
        v-if="!AB_TESTS_NAVIGATION_2025 && !isMobileOrTabletViewport"
        to="#nav-portal"
      >
        <LazyPdpProductBar
          hydrate-on-idle
          v-bind="{
            title: seoTitle,
            material : furnitureDynamicParameters?.materialValue,
            shelfType: furnitureDynamicParameters?.shelfType,
            regionName,
            minDelivery,
            maxDelivery,
            configuratorType,
            furnitureCategory,
            productModel: properModel,
            prices: furnitureDynamicParameters.prices,
          }"
        />
      </Teleport>
      <LazyPdpProductBar
        v-else
        hydrate-on-idle
        v-bind="{
          title: seoTitle,
          material : furnitureDynamicParameters?.materialValue,
          shelfType: furnitureDynamicParameters?.shelfType,
          regionName,
          minDelivery,
          maxDelivery,
          configuratorType,
          furnitureCategory,
          productModel: properModel,
          prices: furnitureDynamicParameters.prices,
        }"
      />
    </ClientOnly>

    <NuxtErrorBoundary>
      <section
        id="pdp-section-configurator"
        ref="configurator"
        data-observe-view="configurator"
        class="relative configurator-main-wrapper configurator-main-wrapper--padding z-3"
        :class="{ 'configurator-main-wrapper--step': [6, 7, 8].includes(shelfType as number) }"
      >
        <LazyPdpConfigurator
          hydrate-on-idle
          :configurator-meta-data="configuratorMetaData"
          :title="seoTitle"
          v-on:scroll-to-product-details="() => scrollToElement({ element: '#pdp-section-product-details', offset: 32 })"
        />
        <PdpConfiguratorLoader
          v-show="loader"
          :class="{
            'configurator-loader--modern': AB_TESTS_NAVIGATION_2025,
          }"
          v-bind="{
            price,
            depth,
            width,
            height,
            preview,
            material,
            shelfType,
            minDelivery,
            maxDelivery,
            gridAllColors,
            title: seoTitle,
            configuratorType,
            configuratorPreview,
            salePrice: priceWithDiscount
          }"
        />
      </section>
    </NuxtErrorBoundary>

    <LazyPdpUsps
      hydrate-on-visible
      data-observe-view="USP bar"
      v-bind="{
        shelfType: furnitureDynamicParameters?.shelfType,
        regionName,
        assemblyFree,
        reviewsCount,
        assemblyAvailable,
        reviewsAverageScore
      }"
    />

    <div class="relative">
      <div
        v-if="AB_TESTS_NAVIGATION_2025"
        class="-mb-48 h-80 lg:-mb-52 bg-beige-100"
      />
      <template
        v-else
      >
        <LazyPdpNavigation
          hydrate-on-visible
          v-bind="{ isABTest: !regionWithoutTone && !isMobileOrTabletViewport }"
        />
      </template>

      <LazyPdpGallery
        id="pdp-section-gallery"
        hydrate-on-visible
        data-observe-view="Gallery"
        v-bind="{
          alt: seoTitle,
          configuratorType,
          category: furnitureCategory,
          isABTest: !regionWithoutTone,
          depth: furnitureDynamicParameters.depth,
          shelfType: furnitureDynamicParameters.shelfType,
          furnitureType: furnitureDynamicParameters.furnitureType,
          materialValue: furnitureDynamicParameters.materialValue,
        }"
      />

      <div id="pdp-section-product-details">
        <LazyPdpProductDetails
          hydrate-on-visible
          data-observe-view="Specification"
          v-bind="{
            configuratorType,
            furnitureCategory,
            furnitureDynamicParameters
          }"
        />

        <LazyPdpLineHero
          hydrate-on-visible
          v-bind="{
            furnitureType,
            furnitureCategory,
            shelfType: furnitureDynamicParameters.shelfType,
            material: furnitureDynamicParameters.materialValue
          }"
        />

        <div
          v-if="isHighlightsSectionVisible"
          class="overflow-hidden"
        >
          <UiAnimatedHeadlineSection
            id="section-highlights"
            data-observe-view="highlights"
            class="relative pb-64 z-1 md:pb-80 xl:pb-144"
            data-section="section-highlights"
            v-bind="{
              headingCopy: $t(`pdp.animation-highlights.${['bedsidetable', 'chest', 'sideboard', 'tvstand'].includes(currentFurnitureCategory.name) ? 'tone' : 'original'}-shelves.headline.${currentFurnitureCategory.name}`),
              subheadingCopy: $t(`pdp.animation-highlights.${['bedsidetable', 'chest', 'sideboard', 'tvstand'].includes(currentFurnitureCategory.name) ? 'tone' : 'original'}-shelves.subheadline.${currentFurnitureCategory.name}`),
            }"
          >
            <LazyPdpVideoTilesCarousel
              hydrate-on-visible
              class="grid-container"
              v-bind="{
                categoryName: currentFurnitureCategory.name,
                shelfType: furnitureDynamicParameters.shelfType
              }"
            />
          </UiAnimatedHeadlineSection>
        </div>
        <template v-else>
          <LazyPdpProductFeatures
            hydrate-on-visible
            data-observe-view="Additional info"
            v-bind="{
              furnitureType,
              furnitureCategory,
              material: furnitureDynamicParameters.materialValue
            }"
          />
        </template>
      </div>

      <LazyPdpReviews
        hydrate-on-visible
        data-observe-view="Reviews"
        v-bind="{
          category: furnitureCategory
        }"
      />

      <template v-if="furnitureCategory !== 'dressing_table'">
        <LazyPdpInstagramHero
          hydrate-on-visible
          data-observe-view="Get inspired"
          v-bind="{
            furnitureCategory,
            shelfType: furnitureDynamicParameters.shelfType
          }"
        />
      </template>

      <LazyPdpLobby
        hydrate-on-visible
        data-observe-view="Discover your perfect fit"
        v-bind="{
          furnitureType,
          furnitureCategory,
          shelfType: furnitureDynamicParameters.shelfType,
        }"
      />

      <LazySectionMinigrid
        hydrate-on-visible
        data-observe-view="You might like"
        data-section="minigrid-pdp"
        v-bind="{
          title: $t('pdp.minigrid.headline'),
          label: $t('pdp.minigrid.subheadline'),
          id: MINIGRID_DATA[furnitureCategory as keyof typeof MINIGRID_DATA],
          boardId: MINIGRID_DATA[furnitureCategory as keyof typeof MINIGRID_DATA],
          itemListName: 'minigrid-pdp'
        }"
      />

      <section
        id="showrooms-map"
        class="bg-beige-100 pt-48 lg:pt-80"
      >
        <div class="grid-container pb-32 md:pb-48">
          <UiHeadline
            class="lg:mb-32"
            v-bind="{
              label: $t('pdp.showroom_tagline'),
              title: $t('pdp.showroom_header'),
              isDarkMode: false
            }"
          />
        </div>
      </section>

      <ClientOnly>
        <LazyPdpMap hydrate-on-visible />
      </ClientOnly>

      <LazySanityContent
        v-if="sanityFetchedData && sanityFetchedData['pageBuilder']"
        hydrate-on-visible
        v-bind="{
          serializers,
          blocks: sanityFetchedData['pageBuilder'],
        }"
      />

      <LazyPdpFloatingSection hydrate-on-visible />

      <LazyPdpDrawers
        v-bind="{
          furnitureCategory,
          configuratorType,
          shelfType: furnitureDynamicParameters?.shelfType || shelfType,
          material: furnitureDynamicParameters?.materialValue || material
        }"
      />
    </div>
  </main>
</template>

<script lang="ts" setup>
import { useIntersectionObserver } from '@vueuse/core';
import useMq from '~/composables/useMq';
import useSeo from '~/composables/useSeo';
import scrollToElement from '~/helpers/scrollToElement';

import { usePdpStore } from '~/stores/pdp';
import { useGlobal } from '~/stores/global';
import { GET_SHELF_TYPE } from '~/utils/types';
import { serializers } from '~/utils/sanitySerializers';
import { pdpAnalytics } from '~/composables/pdp/pdpAnalytics';
import { SHORT_MODEL_TO_MODEL } from '~/utils/configuratorTypeToModel';
import { useTrackSectionView } from '~/composables/useTracking';

import { INITIAL } from '~/api/pdp';
import type { ExternalPathsResponse } from '~/server/routes/nuxt-api/configurators/paths/[type]';

const $gtm = useGtm();
const route = useRoute();
const sanity = useSanity();
const pdpStore = usePdpStore();
const globalStore = useGlobal();
const config = useRuntimeConfig();

const { $i18n } = useNuxtApp();
const { locale } = useLocale();
const { categories } = useCategories();
const { isMobileOrTabletViewport } = useMq();
const { isConfiguratorVisible } = useConfigurator();

const { pdpJsonLd } = useSeo();
const { getColor } = useColors();

const {
  regionName,
  t03Available,
  assemblyAvailable,
  AB_TESTS_NAVIGATION_2025
} = storeToRefs(globalStore);

const {
  price,
  width,
  depth,
  height,
  reviews,
  preview,
  pricing,
  delivery,
  material,
  shelfType,
  seoTitles,
  priceInEuro,
  patternName,
  reviewsCount,
  assemblyFree,
  furnitureType,
  gridAllColors,
  configuratorType,
  priceWithDiscount,
  furnitureCategory,
  configuratorPreview,
  reviewsAverageScore,
  priceWithDiscountInEuro
} = storeToRefs(pdpStore);

const loader = ref(true);
const sanityFetchedData = ref(null);
const configuratorPayload = ref({});
const maxDelivery = ref(delivery.value && delivery.value.max);
const minDelivery = ref(delivery.value && delivery.value.min);

const { category: currentRouteCategory, furniture } = route.params;

const [productId, productModel] = furniture.split('?').join(',').split(',');

const properModel = SHORT_MODEL_TO_MODEL(productModel);

const categoryData = Object.values(categories).find((cat) => {
  const categoryTranslation = $i18n.t(cat.pdpUrlCategoryKey).replaceAll('/', '');

  return currentRouteCategory === categoryTranslation;
}) || {};

const { data, error }: { data: any, error: any } = await INITIAL(properModel, productId, locale.value, `${config.public.baseURL}${route.path}`);

const fetchSanityData = async () => {
  const furnitureType = GET_SHELF_TYPE(shelfType.value);
  const colorName = getColor(shelfType.value, material.value)?.name;

  const query = furnitureType === '01v'
    ? groq`*[_type == "productFurniturePage" && name == "type${colorName}-${furnitureCategory.value}" && language == "${locale.value}"][0]`
    : groq`*[_type == "productFurniturePage" && name == "type${furnitureType}-${furnitureCategory.value}" && language == "${locale.value}"][0]`;

  sanityFetchedData.value = await sanity.fetch(query);
};

const { public: { baseURL } } = useRuntimeConfig();
const configuratorMetaData = shallowRef();

const fetchConfiguratorPaths = async (configuratorType: number) => {
  if (![1, 2, 3].includes(configuratorType)) { return; }

  const { data } = await useApi<ExternalPathsResponse>(`/nuxt-api/configurators/paths/${configuratorType}`);

  if (data.value) {
    configuratorMetaData.value = {
      link: data.value.css.map(file => ({ rel: 'stylesheet', href: baseURL + file })),
      script: data.value.js.map(file => ({ src: baseURL + file, defer: true }))
    };
  }
};

useTrackSectionView('pdp_section_view', 'view');

const shelfTypeForHeader = useState('shelfType', () => null);
const configuratorTypeForHeader = useState('configuratorType', () => null);

if (error.value) {
  navigateTo('/404');
} else {
  shelfTypeForHeader.value = data.value.shelfType;
  configuratorTypeForHeader.value = data.value.configuratorType;
  pdpStore.SET_PDP_DATA({
    ...data.value,
    model: properModel,
    category: categoryData?.categoryIndex,
    furnitureType: GET_SHELF_TYPE(data.value.shelfType)
  });

  if (data.value) {
    await Promise.all([
      fetchSanityData(data.value),
      fetchConfiguratorPaths(data.value.configuratorType)
    ]);
  }
}

// Section High-lights (Video tiles carousel) visible only for certain categories
const currentFurnitureCategory = computed(() => categories[furnitureCategory.value]);
const isHighlightsSectionVisible = computed(() => [0, 1, 2, 6, 7, 8].includes(shelfType.value));

const updateConfiguratorPayload = (updatedConfiguratorPayload: any) => {
  configuratorPayload.value = updatedConfiguratorPayload;
  updatedConfiguratorPayload.shelfType && (configuratorPayload.value.furnitureType = GET_SHELF_TYPE(updatedConfiguratorPayload.shelfType));
};

const furnitureInitialParameters = computed(() => ({
  height: height.value ? Math.round(height.value / 10) : null,
  width: width.value ? Math.round(width.value / 10) : null,
  depth: depth.value ? Math.round(depth.value / 10) : null,
  materialValue: material.value,
  furnitureStyle: 'grid',
  shelfType: shelfType.value,
  furnitureType: furnitureType.value,
  prices: {
    price: price.value || null,
    priceWithDiscount: priceWithDiscount.value || null
  }
}));

const furnitureDynamicParameters = computed(() => ({
  ...furnitureInitialParameters.value,
  ...configuratorPayload.value
}));

const seoTitle = ref(seoTitles.value ? seoTitles.value[furnitureDynamicParameters.value.shelfType as number][furnitureDynamicParameters.value.materialValue as number] : '');

const regionWithoutTone = shelfType.value === 3 && !t03Available.value;

watch(() => [furnitureDynamicParameters.value.materialValue, furnitureDynamicParameters.value.shelfType], ([material, shelfType]) => {
  seoTitle.value = seoTitles.value ? seoTitles.value[shelfType as number][material as number] : '';
  window?.PubSub?.publish('titleHasChanged', seoTitle.value);
});

const MINIGRID_DATA = {
  dressing_table: 'dressing_table_pdp',
  bedside_table: 'bedside_table_pdp',
  bookcase: 'bookcase_pdp',
  chest: 'chest_pdp',
  desk: 'desk_pdp',
  shoerack: 'shoerack_pdp',
  sideboard: 'sideboard_pdp',
  tvstand: 'tvstand_pdp',
  vinyl_storage: 'vinyl_storage_pdp',
  wallstorage: 'wallstorage_pdp',
  wardrobe: 'wardrobe_pdp'
} as const;

onMounted(() => {
  if (window.PubSub) {
    window.PubSub.subscribe('configuratorInitialized', () => {
      loader.value = false;
    });

    window.PubSub.subscribe('configuratorParametersChange', (_: any, payload: any) => {
      updateConfiguratorPayload(payload);
    });
  }

  if ($gtm) {
    const { viewItemGA4 } = pdpAnalytics({
      material: material.value,
      shelfType: shelfType.value,
      priceInEuro: priceInEuro.value,
      patternName: patternName.value,
      configuratorType: configuratorType.value,
      furnitureCategory: furnitureCategory.value,
      priceWithDiscountInEuro: priceWithDiscountInEuro.value
    });

    $gtm.push({ ecommerce: null });
    $gtm.push(viewItemGA4(productId));
  }
});

definePageMeta({
  middleware: ['pdp-middleware'],
  tag: ['pdp', 'showrooms']
});

const configurator = ref();

const { stop: stopConfiguratorIntersectionObserver } = useIntersectionObserver(
  configurator,
  ([{ isIntersecting }]) => {
    isConfiguratorVisible.value = isIntersecting;
  }
);

onBeforeUnmount(() => {
  stopConfiguratorIntersectionObserver();
});

useSeoMeta({
  title: () => seoTitle.value || '',
  ogTitle: () => seoTitle.value || '',
  description: () => $i18n.te(`pdp.${furnitureCategory.value}.meta.description`) ? $i18n.t(`pdp.${furnitureCategory.value}.meta.description`) : '',
  ogDescription: () => $i18n.te(`pdp.${furnitureCategory.value}.meta.description`) ? $i18n.t(`pdp.${furnitureCategory.value}.meta.description`) : '',
  ogLocale: () => locale.value,
  ogUrl: () => route.path
});

useHead(() => ({
  script: [
    { hid: 'inline-fb-product' },
    {
      type: 'text/javascript',
      innerHTML: `window.fb_content_type = "product";window.content_ids = ["${productId}"];`
    }
  ]
}));
// @ts-expect-error
useJsonld(pdpJsonLd(
  reviews.value,
  productId,
  preview.value,
  seoTitle.value,
  config.public.baseURL,
  route.path,
  priceWithDiscount.value,
  price.value,
  pricing.value ? pricing.value.priceIso : 'EUR',
  reviewsCount.value,
  reviewsAverageScore.value,
  $i18n.t(`pdp.${furnitureCategory.value}.meta.description`)
));
</script>

<style lang="scss">
@import '~/assets/scss/ds/ty-configurator.scss';
</style>
