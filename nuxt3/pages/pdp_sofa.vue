<template>
  <main
    class="reset-modern-navigation-page-padding overflow-x-hidden"
    :class=" { 'flex flex-col': isCoverCategory }"
  >
    <div
      class="block lg:grid lg:grid-cols-[48px_57fr_43fr_48px] xl:grid-cols-[56px_62fr_38fr_56px] xl2:grid-cols-[1fr_942px_578px_1fr]
              bg-beige-200 relative"
      :class=" { 'order-1': isCoverCategory }"
    >
      <div class="col-span-2 gallery-slide-aspect">
        <BaseCarousel
          ref="galleryCarousel"
          class="sofa-gallery-carousel group lg:-mr-48"
          data-testid="sofa-pdp-gallery"
          swiper-root-classes="!overflow-visible"
          v-bind="{
            options: {
              pagination: {
                renderBullet: (index, className) => !isCoverCategory && (index === 1) ?
                  `<li class='swiper-pagination-bullet--video  ${className}'><svg fill='none' height='8' viewBox='0 0 6 8' width='6' xmlns='http://www.w3.org/2000/svg'><path d='m6 4-6.00000033 3.4641.00000031-6.928202z' fill='currentColor'/></svg></li>`
                  : `<li class='${className} !bg-white'></li>`
              },
              slidesPerView: 'auto',
              loop: !isCoverCategory,
              breakpoints: 0,
              slidesPerGroup: 1,
              loopAdditionalSlides: 1,
              loopAddBlankSlides: true,
              navigation: {
                lockClass: 'hidden',
                disabledClass: 'swiper-button-off',
                nextEl: `#nextp-`,
                prevEl: `#prevp-`,
              },
            },
            name: `product_`,
            isNavigation: false,
            isPaginationDesktop: true,
            isPaginationMobile: true,
          }"
        >
          <template #pagination>
            <div
              class="hidden lg:block opacity-0 group-hover:opacity-100 transition-opacity basic-transition
                      absolute inset-0 z-1 pointer-events-none"
            >
              <button
                id="prevp-"
                disabled
                class="rounded-full bg-white p-12 pointer-events-auto shadow-icon
                        absolute top-1/2 left-24 -translate-y-1/2
                        [&:disabled]:opacity-40 transition-opacity basic-transition"
                data-testid="sofa-pdp-gallery-caret-left"
              >
                <IconCaretLeft class="w-24 h-24" />
              </button>
              <button
                id="nextp-"
                disabled
                class="rounded-full bg-white p-12 pointer-events-auto shadow-icon
                        absolute top-1/2 right-64 xl:right-80 -translate-y-1/2
                        [&:disabled]:opacity-40 transition-opacity basic-transition"
                data-testid="sofa-pdp-gallery-caret-right"
              >
                <IconCaretRight class="w-24 h-24" />
              </button>
            </div>
          </template>
          <template #default="{ activeIndex }">
            <BaseCarouselSlide
              v-for="(asset, index) in galleryCarouselAssets"
              :key="`sofa_${index}`"
              class="gallery-slide-aspect w-full overflow-hidden"
              data-testid="sofa-pdp-gallery-asset"
            >
              <picture
                v-if="asset.type === 'img'"
                class="w-full h-full object-cover"
              >
                <source
                  :srcset="asset.path?.mobile"
                  media="(max-width: 1023px)"
                  type="image/webp"
                >
                <source
                  :srcset="asset.path?.desktop"
                  media="(min-width: 1024px)"
                  type="image/webp"
                >
                <img
                  :src="asset.path?.default"
                  class="w-[calc(100%+2px)] h-[calc(100%+2px)] lg:w-[calc(100%+4px)] lg:h-[calc(100%+4px)] xl:w-[calc(100%+8px)] xl:h-[calc(100%+8px)]  object-cover"
                  data-testid="sofa-pdp-gallery-image"
                  alt=""
                >
              </picture>
              <BaseVideoCloudinary
                v-else-if="asset.type === 'video'"
                class="w-full h-full"
                data-testid="sofa-pdp-gallery-video"
                type="M T SD LD XLD"
                v-bind="{
                  active: index === activeIndex,
                  path: asset.path,
                  posterPath: `${asset.path}/poster`,
                }"
              />
              <BasePicture
                v-else
                class="w-full h-full object-cover"
                data-testid="sofa-pdp-gallery-picture"
                img-classes="w-full h-full object-cover"
                type="M T SD LD XLD"
                v-bind="{
                  disableLazy: index === 0,
                  isRetinaUploaded: false,
                  alt: '',
                  path: asset.path,
                }"
              />
            </BaseCarouselSlide>
          </template>
        </BaseCarousel>
      </div>

      <div
        class="relative z-2"
        data-section="section-product-card"
      >
        <PdpSofaProductInfo
          v-if="!isCoverCategory"
          :product-id="productId"
          :class="AB_TESTS_NAVIGATION_2025 ? 'lg:mt-120' : 'lg:mt-24'"
          class="lg:absolute z-2 lg:top-0 lg:right-0 lg:shadow-icon"
        />
        <PdpSofaCovers
          v-else
          :product-id="productId"
          :class="AB_TESTS_NAVIGATION_2025 ? 'lg:mt-120' : 'lg:mt-24'"
          class="lg:absolute z-2 lg:top-0 lg:right-0 lg:shadow-icon"
        />
      </div>
    </div>

    <LazyPdpUsps
      v-if="!isCoverCategory"
      hydrate-on-visible
      data-observe-view="USP bar"
      v-bind="{
        shelfType: 10,
        regionName,
        reviewsCount,
        reviewsAverageScore
      }"
    />

    <UiAnimatedHeadlineSection
      v-if="!isCoverCategory"
      id="section-highlights"
      data-observe-view="highlights"
      class="relative pb-64 z-1 md:pb-80 xl:pb-144"
      data-testid="sofa-pdp-highlights-heading"
      data-section="section-highlights"
      v-bind="{
        headingCopy: $t('pdp.sofa.modular.title'),
        subheadingCopy: furnitureCategory === 'armchair' ? $t('pdp.sofa.modular.tagline.armchair') : $t('pdp.sofa.modular.tagline.default'),
        sectionWrapperClasses: 'bg-beige-200'
      }"
    >
      <LazyPdpVideoTilesCarousel
        hydrate-on-visible
        class="grid-container"
        data-testid="sofa-pdp-highlights-tiles"
        v-bind="{
          categoryName: 'sofa',
          shelfType: 10
        }"
      />
    </UiAnimatedHeadlineSection>

    <LazyPdpSofaProductDetails
      hydrate-on-visible
      v-bind="{
        productId
      }"
      :class=" { 'order-2': isCoverCategory }"
      data-testid="sofa-pdp-product-details"
    />

    <template v-if="!isCoverCategory">
      <LazySectionVideo
        id="hp-hero-video"
        hydrate-on-visible
        data-observe-view="hero-video"
        data-section="hero-video"
        data-testid="hero-video"
        has-sound
        fill-viewport
        v-bind="{
          videoParams:{
            videoId: {
              mobile: 'tev53xevzc',
              desktop: 'z6tsm2vuif'
            },
            aspectRatio: {
              mobile: '1178/1768',
              desktop: '1920/960'
            }
          },
          ctaUrl: $addLocaleToPath('change-category'),
          ctaCopy: $t('pdp.sofa.smooth.cta'),
          title: $t('pdp.sofa.smooth.title'),
          description: $t('pdp.sofa.smooth.description'),
          videoPlaceHolder: 'homepage/video-placeholders/sofs_lunch',
        }"
      />
    </template>

    <template v-if="!isCoverCategory">
      <LazyPdpPlusPopups />
      <LazyPdpSofaSamples />
    </template>

    <section
      id="value_proposition"
      data-testid="sofa-pdp-value-proposition"
      :class="{ 'order-3': isCoverCategory }"
    >
      <div class="py-48 md:py-64 xl:py-96 bg-beige-200">
        <div class="grid-container">
          <p class="uppercase semibold-12 text-neutral-750 mb-8">
            {{ $t('pdp.sofa.highlights.tagline') }}
          </p>
          <h3 class="semibold-28 lg:semibold-44 xl:semibold-54 text-neutral-900 mb-32 lg:mb-48 max-w-[635px]">
            {{ $t('pdp.sofa.highlights.title') }}
          </h3>
        </div>

        <LazySectionFourTiles hydrate-on-visible>
          <template #tile1>
            <CardDesign
              v-bind="{
                imgPath: 'pdp/sofa/longevity/1',
                imgAlt: 'lorem',
                imgType: 'M T SD LD XLD',
                index: 1,
                heading: $t('pdp.sofa.highlights.carousel.item1.title'),
                copy: $t('pdp.sofa.highlights.carousel.item1.description')
              }"
            />
          </template>
          <template #tile2>
            <CardDesign
              v-bind="{
                imgPath: 'pdp/sofa/longevity/2',
                imgAlt: 'lorem',
                imgType: 'M T SD LD XLD',
                index: 2,
                heading: $t('pdp.sofa.highlights.carousel.item2.title'),
                copy: $t('pdp.sofa.highlights.carousel.item2.description')
              }"
            />
          </template>
          <template #tile3>
            <CardDesign
              v-bind="{
                imgPath: 'https://media.tylko.com/cloudinary/pdp/sofa/longevity/gif',
                gif: true,
                imgAlt: 'lorem',
                imgType: 'M T SD LD XLD',
                index: 3,
                heading: $t('pdp.sofa.highlights.carousel.item3.title'),
                copy: $t('pdp.sofa.highlights.carousel.item3.description')
              }"
            />
          </template>
          <template #tile4>
            <CardDesign
              v-bind="{
                imgPath: 'pdp/sofa/longevity/4',
                imgAlt: 'lorem',
                imgType: 'M T SD LD XLD',
                index: 4,
                heading: $t('pdp.sofa.highlights.carousel.item4.title'),
                copy: $t('pdp.sofa.highlights.carousel.item4.description')
              }"
            />
          </template>
        </LazySectionFourTiles>
      </div>
    </section>
    <div
      :class="{ 'order-4': isCoverCategory }"
    >
      <LazyPdpReviews
        hydrate-on-visible
        data-observe-view="Reviews"
        data-testid="sofa-pdp-reviews"
        extra-classes="lg-max:pt-32"
        v-bind="{
          category: furnitureCategory
        }"
      />
    </div>

    <LazySectionCreators
      id="pdp-creators"
      :class=" { 'order-5': isCoverCategory }"
      data-testid="sofa-pdp-creators"
      hydrate-on-visible
      data-observe-view="creators"
      data-section="creators"
      v-bind="{
        tagline: $t('pdp.sofa.creators.tagline'),
        headline: $t('pdp.sofa.creators.headline'),
        buttonHref: $addLocaleToPath('lp.influencers'),
        carouselName: 'creators',
        items: creators
      }"
    />

    <div
      class="py-48 md:py-64 xl:py-96"
      :class="[isCoverCategory ? 'order-6 bg-beige-100' : 'bg-beige-200']"
    >
      <div class="grid-container">
        <p class="uppercase semibold-12 text-neutral-750 mb-8">
          {{ $t('pdp.sofa.minigrid.tagline') }}
        </p>
        <div class="md:flex justify-between items-start">
          <h3 class="semibold-28 lg:semibold-44 xl:semibold-54 text-neutral-900 max-w-[635px]">
            {{ $t('pdp.sofa.minigrid.title') }}
          </h3>
          <BaseLink
            v-bind="{
              href: $addLocaleToPath('plp'),
              variant: 'outlined',
              trackData: { eventLabel: 'cta', eventPath: $addLocaleToPath('plp') }
            }"
            class="md-max:mt-16"
          >
            {{ $t('common.view_all') }}
          </BaseLink>
        </div>
      </div>
      <LazySectionMinigrid
        hydrate-on-visible
        data-observe-view="hp-minigrid"
        data-section="hp-minigrid"
        class="!pt-32"
        data-testid="sofa-pdp-minigrid"
        v-bind="{
          id: 'hp_minigrid-browse-by-room',
          itemListName: 'mini_grid',
          boardId: 'hp_rooms_living-room',
          additionalClasses: '!mt-0',
          ctaLink: $addLocaleToPath('plp'),
        }"
      />
    </div>

    <section
      id="showrooms-map"
      :class=" { 'order-7': isCoverCategory }"
      class="bg-beige-200"
    >
      <div class="grid-container pt-48 md:pt-64 lg:pt-80 xl:pt-96 pb-32 md:pb-48">
        <p class="uppercase semibold-12 text-neutral-750 mb-8">
          {{ $t('pdp.showroom_tagline') }}
        </p>
        <div class="md:flex justify-between items-start">
          <h3 class="semibold-28 lg:semibold-44 xl:semibold-54 text-neutral-900">
            {{ $t('pdp.showroom_header') }}
          </h3>
        </div>
      </div>
    </section>

    <LazyPdpMap
      hydrate-on-visible
      :class=" { 'order-8': isCoverCategory }"
    />

    <LazyPdpDrawerSofaSamples
      v-if="isSamplesDrawerHydrate"
      v-model="isSamplesDrawerOpen"
    />
  </main>
</template>

<script lang="ts" setup>
import { useEventBus } from '@vueuse/core';
import { useTrackSectionView } from '~/composables/useTracking';
import { getSottyMaterialById, SofaFinishType } from '~/consts/shelfType10';
import { creators } from '~/consts/homepage/creators';
import type { BaseCarousel } from '#components';

const isSamplesDrawerOpen = ref(false);
const isSamplesDrawerHydrate = ref(false);
const samplesDrawerEventBus = useEventBus('sofa-samples-drawer');

onMounted(() => {
  samplesDrawerEventBus.on((event) => {
    if (event === 'openSamplesDrawer') {
      isSamplesDrawerHydrate.value = true;
      isSamplesDrawerOpen.value = true;
    }
  });
});

onBeforeUnmount(() => samplesDrawerEventBus.reset());

const route = useRoute();
const pdpStore = usePdpStore();

const { AB_TESTS_NAVIGATION_2025, regionName } = useGlobal();
const { reviews, reviewsCount, reviewsAverageScore, furnitureCategory, fabric, galleryDesktopImage, galleryMobileImage } = storeToRefs(pdpStore);

const { $addLocaleToPath } = useNuxtApp();

const productId = ref<string>();

watch(() => route?.params?.furniture,
  (newProductId): void => {
    productId.value = String(newProductId)?.split(',')[0] || '';
  }, { immediate: true });

const isCoverCategory = pdpStore.furnitureCategory === 'cover';

const productMaterial = computed(() => getSottyMaterialById(pdpStore.material!));

const productColorInGalleryPath = computed(() => {
  if (productMaterial.value?.material_name === 'custom') {
    switch (fabric.value) {
      case SofaFinishType.WOOL:
        return getSottyMaterialById(0)?.galleryPaths?.colorPath;

      case SofaFinishType.CORDUROY:
        return getSottyMaterialById(7)?.galleryPaths?.colorPath;
    }
  }

  return productMaterial.value?.galleryPaths.colorPath;
});

useTrackSectionView('pdp_section_view', 'view');

const galleryCarouselAssets = computed(() => {
  return isCoverCategory
    ? [
        { path: `${productMaterial.value?.galleryPaths.path}/covers/closeup/${fabric.value}/${productColorInGalleryPath.value}`, type: 'picture' },
        { path: `${productMaterial.value?.galleryPaths.path}/covers/${pdpStore.moduleName}/${fabric.value}/${productColorInGalleryPath.value}`, type: 'picture' }
      ]
    : [
        { path: { desktop: galleryDesktopImage.value, mobile: galleryMobileImage.value, default: pdpStore.preview }, type: 'img' },
        { path: `${productMaterial.value?.galleryPaths.path}/${fabric.value}/3/`, type: 'video' },
        { path: `${productMaterial.value?.galleryPaths.path}/${fabric.value}/5/${productColorInGalleryPath.value}`, type: 'picture' },
        { path: `${productMaterial.value?.galleryPaths.path}/${fabric.value}/6/${productColorInGalleryPath.value}`, type: 'picture' },
        { path: `${productMaterial.value?.galleryPaths.path}/${fabric.value}/2/`, type: 'picture' },
        { path: `${productMaterial.value?.galleryPaths.path}/${fabric.value}/4/`, type: 'picture' }
      ];
});

definePageMeta({
  key: () => 'pdp',
  middleware: ['pdp-middleware', 'pdp-sofa-middleware'],
  tag: ['pdp', 'pdp_smooth', 'showrooms'],
  scrollToTop: false
});

useHead(() => ({
  script: [
    { hid: 'inline-fb-product' },
    {
      type: 'text/javascript',
      innerHTML: `window.fb_content_type = "product";window.content_ids = ["${productId.value}"];`
    }
  ]
}));
</script>

<style lang="scss">
.gallery-slide-aspect{
  @apply aspect-[393/422] md:aspect-[768/825] lg:aspect-[837/628] xl:aspect-[939/704] xl2:aspect-[1115/836]
}

.base-carousel.sofa-gallery-carousel {
  .swiper-pagination {
    @apply bg-beige-100/80 shadow-icon;
    @apply flex items-center gap-8 p-8 rounded-full;
    @apply absolute bottom-16 lg:bottom-16 left-1/2 transform -translate-x-1/2 #{!important};

    &-bullet {
      @apply bg-white #{!important};
      @apply w-8 h-8 rounded-full border border-neutral-900  cursor-pointer;
    }

    &-bullet--video {
      @apply flex items-center justify-center pl-2;
      @apply w-16 h-16 #{!important};

      &.swiper-pagination-bullet-active{
        @apply text-white;
      }
    }

    &-bullet-active {
      @apply bg-neutral-900 #{!important};
    }
  }
}
</style>
