import { useToast } from 'vue-toastification';
import AdyenCheckout from '@adyen/adyen-web';
import {
  CHANGE_STATUS_TO_DRAFT,
  CHANGE_STATUS_TO_PENDING,
  CHOSEN_PAYMENT_METHOD, PAYMENT_SESSION,
  VALIDATE_FINAL_TRANSACTION_PRICE
} from '~/api/checkout';

import {
  REGISTER
} from '~/api/account';
import { checkoutAnalytics } from '~/composables/checkout/checkoutAnalytics';
import { useScartStore } from '~/stores/scart';

export default function () {
  const { $i18n, $logException } = useNuxtApp();
  const $gtm = useGtm();
  const dropin = useState('dropin', () => null);
  const shouldAllowPayments = useState('shouldAllowPayments', () => null);
  const isDropinReady = useState('isDropinReady', () => false);
  const selectedPaymentMethod = useState('selectedPaymentMethod', () => null);
  const toast = useToast();
  const global = useGlobal();
  const isCheckoutLoading = ref(true);
  const errorMessage = ref(false);
  const isPaymentFailed = useState('isPaymentFailed', () => false);
  const formData = useState('formData', () => null);
  const isNewFormMounted = useState('isFormMounted', () => null);
  const shouldLoadPaymentsOnCheckout = useState('shouldLoadPaymentsOnCheckout', () => null);
  const paymentsMock = useState('paymentsMock', () => false);
  const paypalButtonWrapper = useState('paypalButtonWrapper', () => null);
  const paymentButtonLoader = useState('paymentButtonLoader', () => true);
  const displayPaymentButton = useState('displayPaymentButton', () => true);
  const shouldRenderAdyen = useState('shouldRenderAdyen', () => false);
  const formDisplay = useState('formDisplay', () => true);
  const disablePayments = useState('disablePayments', () => false);
  const disableCardInitialCall = ref(false);
  const { checkout2025Event } = checkoutAnalytics();
  const cart = useScartStore();
  const adyenInstance = useState<null | Awaited<ReturnType<typeof AdyenCheckout>>>('adyenInstance', () => shallowRef(null));
  const {
    email,
    password,
    newsletter,
    signup
  } = storeToRefs(useCheckoutStore());
  let errorTimeout;

  const changeStatusToDraft = async () => {
    const { error } = await CHANGE_STATUS_TO_DRAFT(global);

    if (error.value) {
      toast.error($i18n.t('common.error.connection'));
      $logException(error.value);
    }
  };

  const changeStatusToPending = async () => {
    const { error } = await CHANGE_STATUS_TO_PENDING(global);

    if (error.value) {
      toast.error($i18n.t('common.error.connection'));
      $logException(error.value);
    }
  };

  const finalPriceVerification = async () => {
    const { error, data } = await VALIDATE_FINAL_TRANSACTION_PRICE(global);

    if (error.value) {
      toast.error($i18n.t('common.error.connection'));
      $logException(error.value);
      return false;
    }

    if (data.value.validated === false) {
      toast.error($i18n.t('checkout.payment_methods.price_changed'));
    }

    return data.value.validated;
  };

  const beforeSubmitPayment = async (chosenPaymentMethod) => {
    const { error } = CHOSEN_PAYMENT_METHOD(global, chosenPaymentMethod);

    if (chosenPaymentMethod === 'bankTransfer_IBAN') {
      $gtm.push({ ecommerce: null });
      $gtm.push(checkout2025Event('purchase', 'Bank_transfer_IBAN', 'Home delivery'));
      displayPaymentButton.value = false;
    }

    if (error.value) {
      toast.error($i18n.t('common.error.connection'));
      $logException(error.value);
    }
  };

  const createAdyenConfigProps = (data, isBusinessClient, dataForm) => {
    formData.value = dataForm;
    const isPayPalAllowed = (cartValueEur: number) => cartValueEur >= 50 && cartValueEur <= 2000;

    const removePaymentMethods: string[] = [];

    if (isBusinessClient) {
      removePaymentMethods.push('klarna', 'klarna_account', 'klarna_paynow');
    }

    if (!isPayPalAllowed(cart.totalPrice)) {
      removePaymentMethods.push('alma');
    }

    return {
      environment: data.live ? 'live' : 'test',
      clientKey: data.clientKey,
      session: {
        id: data.session.sessionId,
        sessionData: data.session.sessionData
      },
      removePaymentMethods,
      beforeSubmit: async (paymentData, _, actions) => {
        const isPriceValid = await finalPriceVerification();

        if (isPriceValid) {
          if (paymentData.paymentMethod.type === 'bankTransfer_IBAN') {
            formDisplay.value = false;
          }

          await beforeSubmitPayment(paymentData.paymentMethod.type);

          await changeStatusToPending();

          if (signup.value) {
            await signUp();
          }

          actions.resolve(paymentData);
        } else {
          await changeStatusToDraft();
          actions.reject();
        }
      },
      onPaymentCompleted: async (result) => {
      // Drop-in replaces payment options with error message.
      // We want to reset the widget after a timeout
      // so that user can retry payment.
        console.log('result', result);

        if (result.resultCode === 'Error' || result.resultCode === 'Refused') {
          await changeStatusToDraft();
          errorTimeout = setTimeout(() => {
            dropin.value.update();
          }, 3500);
          isPaymentFailed.value = true;
          toast.error($i18n.t('checkout.payment_methods.payment_failure'));
        } else {
          handlePaymentMethodOnConfirmation();
          window.location.href = `${window.location.origin}/${$i18n.locale.value}/confirmation/${global.orderId}/?uuid=${global.userId}`;
        // await router.push({ name: `confirmation-orderId___${i18n.locale}`, params: { orderId: orderId.value }, query: { uuid: userId.value } });
        }
      },
      onError: async (error) => {
        console.log('error', error);
        await changeStatusToDraft();
        if (error.name === 'CANCEL') { return; }
        errorMessage.value = true;
        toast.error($i18n.t('checkout.payment_methods.payment_failure'));
      }
    };
  };

  const initializeAdyen = async (data, isBusinessClient, dataForm) => {
    formData.value = dataForm;
    const configuration = createAdyenConfigProps(data, isBusinessClient, dataForm);

    try {
      adyenInstance.value = await AdyenCheckout({
        ...configuration,

        paymentMethodsConfiguration: {
          paypal: {
            showPayButton: true
          }
        }
      });

      dropin.value = adyenInstance.value.create('dropin', {
        showPayButton: false,
        onSelect: (state) => {
          selectedPaymentMethod.value = state.data.paymentMethod.type;
          console.log(555, selectedPaymentMethod.value);
          handlePaymentMethodOnConfirmation();

          if (!disableCardInitialCall.value) {
            disableCardInitialCall.value = true;
          } else {
            $gtm.push({ ecommerce: null });
            $gtm.push(checkout2025Event('add_payment_info', state.data.paymentMethod.type, 'Home delivery'));
          }
        }
      });
      isCheckoutLoading.value = false;
      dropin.value.mount('#dropin-container');
    } catch (e) {
      errorMessage.value = true;
      toast.error($i18n.t('checkout.payment_methods.payment_failure'));
      $logException(e);
    }
  };

  const updateAdyen = async (data, isBusinessClient, dataForm) => {
    formData.value = dataForm;
    const configuration = createAdyenConfigProps(data, isBusinessClient, dataForm);

    try {
      adyenInstance.value?.update(configuration);
    } catch (e) {
      errorMessage.value = true;
      toast.error($i18n.t('checkout.payment_methods.payment_failure'));
      $logException(e);
    }
  };

  const submitPayment = () => {
    if (dropin.value) {
      dropin.value.submit();
    } else {
      console.error('Adyen Dropin nie jest gotowy!');
    }
  };

  const signUp = async () => {
    try {
      const { error } = await REGISTER({
        email: email.value,
        password: password.value,
        newsletter: newsletter.value
      });

      if (error) {
        console.error('Registration error:', error);
      }
    } catch (e) {
      console.error('An error occurred during registration:', e);
    }
  };

  const handlePaymentMethodOnConfirmation = () => {
    const selectedPaymentWrapper = document.getElementsByClassName('adyen-checkout__payment-method--selected');

    if (selectedPaymentWrapper.length) {
      const img = selectedPaymentWrapper[0].getElementsByClassName('adyen-checkout__payment-method__image');
      const copy = selectedPaymentWrapper[0].getElementsByClassName('adyen-checkout__payment-method__name');

      if (img[0].getAttribute('src')) {
        localStorage.setItem('confirmationPaymentIcon', img[0].getAttribute('src') as string);
      }

      if (copy[0].textContent) {
        localStorage.setItem('confirmationPaymentCopy', copy[0].textContent as string);
      }
    }
  };

  const handleUpdateAdyen = async (formValues: any) => {
    const paypalButton = document.querySelector('.adyen-checkout__paypal');
    paypalButton?.remove();
    paymentButtonLoader.value = false;
    const { data, error } = await PAYMENT_SESSION(global);

    if (error.value) {
      toast.error($i18n.t('checkout.payment_methods.payment_failure'));
      $logException(error.value);
    } else {
      await updateAdyen(data.value, !!formValues.vat, formValues);
    }

    setTimeout(() => {
      paymentButtonLoader.value = true;
    }, 500);
  };

  return {
    updateAdyen,
    dropin,
    initializeAdyen,
    errorMessage,
    isCheckoutLoading,
    isPaymentFailed,
    errorTimeout,
    submitPayment,
    isDropinReady,
    selectedPaymentMethod,
    paypalButtonWrapper,
    isNewFormMounted,
    shouldLoadPaymentsOnCheckout,
    paymentsMock,
    shouldAllowPayments,
    paymentButtonLoader,
    displayPaymentButton,
    formDisplay,
    formData,
    shouldRenderAdyen,
    handleUpdateAdyen,
    disablePayments
  };
}
