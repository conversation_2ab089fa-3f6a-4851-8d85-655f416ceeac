export const positiveFeedbackPercentage = (reviewsCount: number, reviewsAverageScore: number) => {
  if (!reviewsCount || !reviewsAverageScore) { return 0; }

  const avg = reviewsAverageScore;
  let positivePercentage = 0;

  if (avg >= 4.5) {
    positivePercentage = Math.min(100, 90 + (avg - 4.5) * 20);
  } else if (avg >= 4.0) {
    positivePercentage = 75 + (avg - 4.0) * 30;
  } else if (avg >= 3.5) {
    positivePercentage = 60 + (avg - 3.5) * 30;
  } else if (avg >= 3.0) {
    positivePercentage = 40 + (avg - 3.0) * 40;
  } else if (avg >= 2.5) {
    positivePercentage = 20 + (avg - 2.5) * 40;
  } else {
    positivePercentage = Math.max(0, avg * 8);
  }

  return Math.round(positivePercentage);
};
