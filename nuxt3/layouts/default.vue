<template>
  <main
    id="default-layout"
    v-bind="{
      style: `--ribbon-height:${ribbonHeight}px`,
      class: AB_TESTS_NAVIGATION_2025 ? '[--old-navbar-height:0px] [--navigation-modern-padding:88px] md:[--navigation-modern-padding:96px]'
        : '[--navigation-modern-padding:0px] [--old-navbar-height:56px] md:[--old-navbar-height:64px]'
    }"
  >
    <template v-if="AB_TESTS_NAVIGATION_2025">
      <LazyTheHeaderModern
        hydrate-on-idle
        :variant="themeVariant"
      />
    </template>
    <template v-else>
      <LazyTheHeader hydrate-on-idle />
    </template>

    <NuxtPage class="modern-navigation-page-padding" />

    <slot name="content" />

    <template v-if="isNewsletterVisible">
      <LazySectionNewsletter hydrate-on-visible />
    </template>

    <LazyTheFooter2025 hydrate-on-visible />

    <ClientOnly>
      <TheGlobal />
    </ClientOnly>

    <ScartLazyLoader />

    <nav id="feed">
      <div class="content-card-background" />
    </nav>
  </main>
</template>

<script setup lang="ts">
const { AB_TESTS_NAVIGATION_2025 } = storeToRefs(useGlobal());

const { isNewsletterVisible } = useNewsletter();

const route = useRoute();
const getRouteBaseName = useRouteBaseName();
const routeBaseName = computed(() => getRouteBaseName(route));
const { ribbonHeight } = storeToRefs(useHeaderStore());
const themeVariant = computed(() => {
  const routesWithLightTheme = ['sofa-teaser', 'lp-review-list'];
  const routesWithTransparentTheme = ['homepage'];

  if (routesWithLightTheme.includes(routeBaseName.value)) {
    return 'light';
  } else if (routesWithTransparentTheme.includes(routeBaseName.value)) {
    return 'transparent';
  }

  return 'dark';
});

</script>
