<template>
  <nav
    class="normal-16 h-56 md:h-64 p-4 md:p-8 md-max:-mr-8 lg:p-[6px] rounded-full
            flex items-center justify-end lg:justify-between lg:grow
            transition-all ease-out duration-300"
  >
    <!-- Shop buttons -->
    <TheHeaderModernBarButtons
      class="lg-max:hidden [--width:100px] [--duration:500ms]"
      link-extra-classes="px-24"
      v-bind="{
        buttonsList: shopMenuButtons,
        class: {
          'mega-menu-opened': isMegaMenuOpened
        }
      }"
    />

    <!-- Empty separator -->
    <div class="flex-1 max-w-[200px] lg-max:hidden" />

    <!-- Account buttons -->
    <TheHeaderModernBarButtons
      class="[--width:52px] [--duration:400ms]"
      link-extra-classes="px-12 lg:px-[14px]"
      v-bind="{
        buttonsList: accountMenuButtons,
        class: {
          'mobile-mega-menu-opened': isMegaMenuOpened
        }
      }"
    />
  </nav>
</template>

<script lang="ts" setup>
import { useMounted } from '@vueuse/core';
import { useGlobal } from '~/stores/global';
import useHeaderModern from '~/composables/useHeaderModern';
import IconNotification from '~/assets/icons/notification.svg';
import IconProfile from '~/assets/icons/profile.svg';
import IconHeart from '~/assets/icons/heart.svg';
import IconTrolley from '~/assets/icons/trolley.svg';
import IconHamburger from '~/assets/icons/hamburger.svg';

export interface MenuButtonConfig {
  testId: string;
  href?: string;
  trackData?: {
    eventCategory: string;
    eventAction: string;
    eventLabel: string;
  };
  extraClasses?: string;
  label?: string;
  icon?: any;
  iconExtraClasses?: string;
  component?: any;
  badge?: number | null;
  attributes?: any;
  handlers: { };
}
const { t } = useI18n();
const { cartItemsCount, libraryItemsCount, brazeNotificationCount, isSignedIn, userId } = storeToRefs(useGlobal());
const { $addLocaleToPath, $braze } = useNuxtApp();
const {
  isMegaMenuOpened,
  toggleMegaMenu,
  toggleMobileMegaMenu
} = useHeaderModern();

const isMounted = useMounted();
const accountMenuButtons = computed<Array<MenuButtonConfig>>(() => [
  {
    testId: 'open-braze',
    trackData: {
      eventAction: 'Content Card Show',
      eventCategory: 'Content Card',
      eventLabel: 'Content Card Label'
    },
    icon: IconNotification,
    badge: brazeNotificationCount.value,
    attributes: {
      id: 'braze-button'
    },
    handlers: {
      click: () => $braze?.showFeedDrawer()
    }
  },
  {
    testId: 'redirect-account',
    href: $addLocaleToPath('account'),
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'account',
      eventLabel: 'opened'
    },
    icon: IconProfile,
    handlers: {},
    attributes: {
      asNuxtLink: true
    }
  },
  {
    testId: 'redirect-wishlist',
    href: $addLocaleToPath('library'),
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'favourites',
      eventLabel: 'opened'
    },
    icon: IconHeart,
    badge: libraryItemsCount.value,
    handlers: { }
  },
  {
    testId: 'open-cart',
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'basket',
      eventLabel: 'opened'
    },
    href: `${$addLocaleToPath('cart')}${(isMounted.value && userId.value) ? '?uuid=' + userId.value : ''}`,
    icon: IconTrolley,
    badge: cartItemsCount.value,
    handlers: { },
    attributes: {
      asNuxtLink: true
    }
  },
  {
    testId: 'mobile-menu-burger',
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'hamburger',
      eventLabel: 'opened'
    },
    extraClasses: 'lg:!hidden relative',
    icon: IconHamburger,
    iconExtraClasses: 'w-24 h-24',
    handlers: {
      click: () => toggleMobileMegaMenu()
    }
  }
]
);

const shopMenuButtons = computed<Array<MenuButtonConfig>>(() => [
  {
    testId: 'toggle-products-tab',
    href: $addLocaleToPath('plp'),
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'shop',
      eventLabel: 'opened'
    },
    label: t('menu.labels.shop'),
    attributes: {
      id: 'toggle-products-tab',
      href: $addLocaleToPath('lp.products'),
      trackData: {
        eventCategory: 'navigation',
        eventAction: 'shop',
        eventLabel: 'opened'
      }
    },
    extraClasses: '[.mega-menu-opened_&]:!text-[var(--text-color)]',
    handlers: {
      mouseover: () => (toggleMegaMenu(true))
    }
  },
  {
    testId: 'toggle-influencers-tab',
    href: $addLocaleToPath('lp.influencers'),
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'influencers',
      eventLabel: 'opened'
    },
    label: t('menu.labels.influencers'),
    handlers: {
      mouseover: () => (toggleMegaMenu(false))
    }
  },
  {
    testId: 'toggle-inspirations-tab',
    href: $addLocaleToPath('lp.gallery'),
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'inspiration',
      eventLabel: 'opened'
    },
    label: t('menu.labels.inspiration'),
    handlers: {
      mouseover: () => (toggleMegaMenu(false))
    }
  },
  // {
  //   testId: 'redirect-tylko-pro',
  //   href: $addLocaleToPath('lp.tylkopro'),
  //   trackData: {
  //     eventCategory: 'navigation',
  //     eventAction: 'tylko_for_business',
  //     eventLabel: 'opened'
  //   },
  //   label: t('menu.labels.tylko_pro'),
  //   handlers: {
  //     mouseover: () => (toggleMegaMenu(false))
  //   }
  // },
  {
    testId: 'redirect-showroooms',
    href: $addLocaleToPath('showrooms'),
    trackData: {
      eventCategory: 'navigation',
      eventAction: 'showrooms',
      eventLabel: 'opened'
    },
    label: 'Showrooms',
    handlers: {
      mouseover: () => (toggleMegaMenu(false))
    }
  }
]
);
</script>
