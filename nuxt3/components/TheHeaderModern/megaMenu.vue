<template>
  <Transition
    appear
    enter-from-class="opacity-0"
    leave-to-class="opacity-0"
    enter-active-class="transition-opacity duration-300 ease-out"
    leave-active-class="transition-opacity duration-[150ms] ease-in"
  >
    <div
      v-show="isMegaMenuOpened && isMounted"
      id="mega-menu-backdrop"
      class=" transition-opacity pt-80 w-screen h-screen max-h-[calc(100dvh-var(--ribbon-height))]
              select-none backdrop-blur-xl backdrop-brightness-[.25]"

      v-on:click="toggleMegaMenu(false)"
    >
      <BaseButton
        v-bind="{
          testId: 'mobile-menu-close',
          trackData: {
            eventCategory: 'navigation',
            eventAction: 'mobile-close',
            eventLabel: 'opened'
          },
        }"
        variant="custom"
        class="absolute top-16 right-28 md:top-24 md:right-[54px] text-white lg:hidden"
        v-on:click="() => toggleMobileMegaMenu(false)"
      >
        <IconCloseL />
      </BaseButton>
      <div
        ref="megaMenuScrollArea"
        class="pt-12 h-full overflow-y-scroll"
        v-on:mouseleave="toggleMegaMenu(false)"
      >
        <div class="grid-container h-full">
          <nav
            class="text-white pb-32 lg:py-32 relative grid
                    lg:grid-cols-[auto_auto_auto_auto_minmax(120px,190px)]
                    lg2:grid-cols-[auto_auto_auto_auto_minmax(190px,300px)]
                    xl2:grid-cols-[auto_auto_auto_auto_minmax(220px,330px)]
                    lg:gap-24 lg2:48 xl:gap-64 xl2:gap-96"
          >
            <!-- Main/Home links -->
            <Transition
              enter-from-class="opacity-0"
              leave-to-class="opacity-0"
              enter-active-class="transition-opacity lg-max:absolute lg-max:w-full duration-300 ease-in-out"
              leave-active-class="transition-opacity lg-max:absolute lg-max:w-full duration-300 ease-on-out"
            >
              <div
                v-show="isDesktopViewport || activeMegaMenuTab === 'home'"
                class="space-y-48 lg:space-y-24 xl:space-y-48"
              >
                <!-- Pages Categories -->
                <ul
                  class="semibold-32 lg:semibold-24 xl:semibold-32
                            flex flex-col gap-y-8 xl:gap-y-12"
                >
                  <li class="lg:hidden flex justify-between items-center -mr-2 md:mr-8">
                    <BaseButton
                      class="w-full text-left h-48"
                      variant="underline-link"
                      v-bind="{
                        trackData: {
                          eventCategory: 'megamenu',
                          eventAction: menuSections?.main?.eventAction,
                          eventLabel: menuSections?.main?.items?.[0]?.eventLabel
                        },
                        'data-testid': menuSections?.main?.items?.[0]?.testId
                      }"

                      v-on:click="toggleTab(menuSections?.main?.items?.[0]?.tabName!)"
                    >
                      <template v-if="menuSections?.main?.items?.[0]?.mobileTitleKey">
                        <span class="lg:hidden">{{ $t(menuSections?.main?.items?.[0]?.mobileTitleKey) }}</span>
                        <span class="lg-max:!hidden">{{ $t(menuSections?.main?.items?.[0]?.titleKey) }}</span>
                      </template>
                      <template v-else>
                        {{ $t(menuSections?.main?.items?.[0]?.titleKey) }}
                      </template>
                    </BaseButton>
                    <IconCaret class="block w-48 h-48" />
                  </li>
                  <li
                    v-for="item in menuSections?.main?.items"
                    :key="item.testId"
                    :class="item.classes"
                  >
                    <BaseLink
                      variant="underline-link"
                      class="lg-max:h-48 lg-max:inline-flex items-center"
                      v-bind="{
                        href: item.href,
                        trackData: {
                          eventCategory: 'megamenu',
                          eventAction: item.eventAction,
                          eventLabel: item.eventLabel
                        },
                        'data-testid': item.testId
                      }"
                    >
                      {{ $t(item.titleKey ) }}
                    </Baselink>
                    <span
                      v-if="item.badgeKey"
                      class="relative align-super text-[#FF3C00] semibold-12"
                    >
                      <span class="block absolute -top-12 -right-12">
                        &nbsp;{{ $t(item.badgeKey) }}
                      </span>
                    </span>
                  </li>
                </ul>

                <!-- Pages links -->
                <ul class="semibold-20 lg:semibold-18 xl:semibold-20 lg:space-y-[10px] xl:space-y-12">
                  <li
                    v-for="item in menuSections?.pages?.items"
                    :key="item.testId"
                  >
                    <BaseLink
                      variant="underline-link"
                      v-bind="{
                        href: item.href,
                        trackData: {
                          eventCategory: 'megamenu',
                          eventAction: item.eventAction,
                          eventLabel: item.eventLabel
                        },
                        'data-testid': item.testId,
                      }"
                      class="leading-1_3 lg-max:py-8 inline-block"
                    >
                      {{ $t(item.titleKey ) }}
                      <template #badge>
                        <span
                          v-if="item.badgeKey"
                          class="relative align-super text-[#FF3C00] semibold-12"
                        >
                          <span class="block absolute -top-2 left-2">
                            {{ $t(item.badgeKey) }}
                          </span>
                        </span>
                      </template>
                    </Baselink>
                  </li>
                </ul>

                <!-- Promo image-->
                <TheHeaderModernBanner class="lg:hidden" />
              </div>
            </Transition>

            <!-- Mobile expanded menu -->
            <Transition
              enter-from-class="opacity-0"
              leave-to-class="opacity-0"
              enter-active-class="transition-opacity lg-max:absolute duration-300 ease-in-out"
              leave-active-class="transition-opacity lg-max:absolute duration-300 ease-on-out"
            >
              <ul
                v-show="activeMegaMenuTab === 'products'"
                class="semibold-32 space-y-16 w-full lg:hidden mt-4 mb-24"
              >
                <template
                  v-for="(section, index) in mobileProductsSections"
                  :key="index"
                >
                  <li
                    v-if="!section?.items"
                    class="mobile-link"
                  >
                    <BaseLink
                      variant="underline-link"
                      v-bind="{
                        href: section.href,
                        trackData: {
                          eventCategory: 'megamenu',
                          eventAction: section.eventAction,
                          eventLabel: section.eventLabel
                        },
                        'data-testid': section.testId,
                      }"
                      class="leading-1_3"
                    >
                      {{ $t(section.titleKey ) }}
                      <template #badge>
                        <span
                          v-if="section.badgeKey"
                          class="navi-badge"
                        >
                          <span>
                            {{ $t(section.badgeKey) }}
                          </span>
                        </span>
                      </template>
                    </Baselink>
                  </li>
                  <BaseAccordion
                    v-else
                    as="li"
                    class="mobile-accordion"
                    v-bind="{
                      isExpanded: true,
                      'data-testid': `accordion-tab-${section.testId}`,
                    }"
                  >
                    <template #title="{ isExpanded }">
                      <div class="flex justify-between items-center text-white">
                        <span>
                          {{ $t(section.titleKey) }}
                          <span
                            v-if="section.badgeKey"
                            class="align-super text-[#C1FF00] semibold-12"
                            :class="section.badgeKey === 'plp.board.product_card.label.sale' ? '!text-orange-500' : ''"
                          >
                            {{ $t(section.badgeKey) }}
                          </span>
                        </span>
                        <component
                          :is="isExpanded ? IconButtonMinus : IconButtonPlus"
                          class="w-32 h-32 mr-[5px] md:mr-[14px]"
                        />
                      </div>
                    </template>
                    <template #content>
                      <ul class="mt-16 pb-32">
                        <li
                          v-for="item in section.items"
                          :key="item.testId"
                        >
                          <BaseLink
                            variant="underline-link"
                            v-bind="{
                              href: item.href,
                              trackData: {
                                eventCategory: 'megamenu',
                                eventAction: item.eventAction,
                                eventLabel: item.eventLabel
                              },
                              'data-testid': item.testId,
                            }"
                            class="normal-18 py-[11px] inline-block"
                          >
                            {{ $t(item.titleKey ) }}
                            <template #badge>
                              <span
                                v-if="item.badgeKey"
                                class="navi-badge text-[#C1FF00]"
                              >
                                <span>
                                  {{ $t(item.badgeKey) }}
                                </span>
                              </span>
                            </template>
                          </Baselink>
                        </li>
                      </ul>
                    </template>
                  </BaseAccordion>
                </template>
              </ul>
            </Transition>

            <!-- Storage-->
            <div class="nav-category-group">
              <BaseLink
                variant="underline-link"
                v-bind="{
                  href: menuSections?.storage?.href,
                  trackData: {
                    eventCategory: 'megamenu',
                    eventAction: menuSections?.storage?.eventAction,
                    eventLabel: menuSections?.storage?.eventLabel
                  },
                  'data-testid': menuSections?.storage?.testId
                }"
              >
                {{ $t(menuSections?.storage?.titleKey) }}
                <template #badge>
                  <span
                    v-if="menuSections?.wardrobes?.badgeKey"
                    class="navi-badge !text-orange-500"
                  >
                    <span>
                      {{ $t(menuSections?.storage?.badgeKey) }}
                    </span>
                  </span>
                </template>
              </Baselink>
              <ul>
                <li
                  v-for="item in menuSections?.storage?.items"
                  :key="item.testId"
                  class="relative"
                >
                  <BaseLink
                    variant="underline-link"
                    v-bind="{
                      href: item.href,
                      trackData: {
                        eventCategory: 'megamenu',
                        eventAction: item.eventAction,
                        eventLabel: item.eventLabel
                      },
                      'data-testid': item.testId
                    }"
                  >
                    {{ $t(item.titleKey ) }}
                    <template #badge>
                      <span
                        v-if="item.badgeKey"
                        class="navi-badge"
                      >
                        <span>
                          {{ $t(item.badgeKey) }}
                        </span>
                      </span>
                    </template>
                  </Baselink>
                </li>
              </ul>
            </div>

            <!-- Wardrobes-->
            <div class="nav-category-group">
              <BaseLink
                variant="underline-link"
                v-bind="{
                  href: menuSections?.wardrobes?.href,
                  trackData: {
                    eventCategory: 'megamenu',
                    eventAction: menuSections?.wardrobes?.eventAction,
                    eventLabel: menuSections?.wardrobes?.eventLabel
                  },
                  'data-testid': menuSections?.wardrobes?.testId
                }"
              >
                {{ $t(menuSections?.wardrobes?.titleKey) }}
                <template #badge>
                  <span
                    v-if="menuSections?.wardrobes?.badgeKey"
                    class="navi-badge !text-orange-500"
                  >
                    <span>
                      {{ $t(menuSections?.wardrobes?.badgeKey) }}
                    </span>
                  </span>
                </template>
              </Baselink>
              <ul>
                <li
                  v-for="item in menuSections?.wardrobes?.items"
                  :key="item.testId"
                >
                  <BaseLink
                    variant="underline-link"
                    v-bind="{
                      href: item.href,
                      trackData: {
                        eventCategory: 'megamenu',
                        eventAction: item.eventAction,
                        eventLabel: item.eventLabel
                      },
                      'data-testid': item.testId
                    }"
                  >
                    {{ $t(item.titleKey ) }}
                    <template #badge>
                      <span
                        v-if="item.badgeKey"
                        class="navi-badge"
                      >
                        <span>
                          {{ $t(item.badgeKey) }}
                        </span>
                      </span>
                    </template>
                  </Baselink>
                </li>
              </ul>
            </div>

            <!-- Sofas -->
            <div class="nav-category-group">
              <template v-if="IS_SMOOTH_AVAILABLE">
                <BaseLink
                  variant="underline-link"
                  v-bind="{
                    href: menuSections?.sofas?.href,
                    trackData: {
                      eventCategory: 'megamenu',
                      eventAction: menuSections?.sofas?.eventAction,
                      eventLabel: menuSections?.sofas?.eventLabel
                    },
                    'data-testid': menuSections?.sofas?.testId
                  }"
                >
                  {{ $t(menuSections?.sofas?.titleKey) }}
                  <template #badge>
                    <span
                      v-if="menuSections?.sofas?.badgeKey"
                      class="navi-badge"
                    >
                      <span class="max-w-[120px] !inline-block !text-wrap">
                        {{ $t(menuSections?.sofas?.badgeKey) }}
                      </span>
                    </span>
                  </template>
                </Baselink>
                <ul>
                  <li
                    v-for="item in menuSections?.sofas?.items"
                    :key="item.testId"
                  >
                    <BaseLink
                      variant="underline-link"
                      v-bind="{
                        href: item.href,
                        trackData: {
                          eventCategory: 'megamenu',
                          eventAction: item.eventAction,
                          eventLabel: item.eventLabel
                        },
                        'data-testid': item.testId
                      }"
                    >
                      {{ $t(item.titleKey ) }}
                      <template #badge>
                        <span
                          v-if="item.badgeKey"
                          class="navi-badge"
                        >
                          <span>
                            {{ $t(item.badgeKey) }}
                          </span>
                        </span>
                      </template>
                    </Baselink>
                  </li>
                </ul>
              </template>
            </div>

            <!-- Promo image-->
            <TheHeaderModernBanner class="lg-max:hidden" />
          </nav>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup lang="ts">
import { useMounted, useScrollLock } from '@vueuse/core';
import type { MagaMenuTabs } from '~/stores/header';
import useMq from '~/composables/useMq';
import { SOTTY_CATEGORIES } from '~/consts/categories';
import IconButtonMinus from '~/assets/icons/minus.svg';
import IconButtonPlus from '~/assets/icons/plus.svg';

const { categories } = useCategories();
const isMounted = useMounted();
const {
  isMegaMenuOpened,
  toggleMegaMenu,
  toggleTab,
  activeMegaMenuTab,
  toggleMobileMegaMenu
} = useHeaderModern();

const { isDesktopViewport } = useMq();
const { $addLocaleToPath } = useNuxtApp();
const { t } = useI18n();
const megaMenuScrollArea = ref<HTMLElement>();
const scrollLock = useScrollLock(window || null);

watch(isMegaMenuOpened, () => {
  if (isMegaMenuOpened.value) {
    scrollLock.value = true;
    nextTick(() => {
      megaMenuScrollArea.value.scrollTop = 0;
    });
  } else {
    scrollLock.value = false;
  }
});
watch(activeMegaMenuTab, () => {
  nextTick(() => {
    megaMenuScrollArea.value?.scrollTo({ top: 0, left: 0, behavior: 'smooth' });
  });
});

const isSamplePromoActive = useFeatureFlag('isSamplePromoActive');
const { IS_SMOOTH_AVAILABLE } = storeToRefs(useGlobal());
const { categories: categoriesInPromotion } = storeToRefs(usePromoStore());
const { $i18n } = useNuxtApp();

const menuSections: {[key: string]: {
  titleKey?: string;
  eventAction: string;
  eventLabel: string;
  testId: string;
  href?: string;
  badgeKey?: string ;
  items?: {
    classes?: string;
    testId: string;
    titleKey: string;
    mobileTitleKey?: string;
    href: string;
    tabName?: MagaMenuTabs;
    eventLabel: string;
    eventAction: string;
    badgeKey?: string ;
  }[];
  }} = {
    main: {
      testId: 'main',
      eventAction: 'main',
      eventLabel: 'main',
      items: [
        {
          classes: 'lg-max:hidden',
          href: `${$addLocaleToPath('plp')}`,
          testId: 'all-products',
          eventLabel: 'all-products',
          eventAction: 'main',
          titleKey: 'common.category.all',
          mobileTitleKey: 'menu.labels.products',
          tabName: 'products'
        },
        {
          classes: 'lg:hidden',
          href: `${$addLocaleToPath('lp.influencers')}`,
          testId: 'creators',
          eventLabel: 'creators',
          eventAction: 'main',
          titleKey: 'menu.labels.influencers'
        },
        {
          classes: 'lg-max:hidden whitespace-nowrap',
          href: `${$addLocaleToPath('plp')}?additional=topSeller`,
          testId: 'bestsellers',
          eventLabel: 'bestsellers',
          eventAction: 'main',
          titleKey: 'common.megamenu.bestsellers'
        },
        {
          classes: 'text-orange font-bold order-first',
          href: `${$addLocaleToPath('plp')}`,
          testId: 'summer-sale',
          eventLabel: 'summer-sale',
          eventAction: 'main',
          titleKey: 'menu.category_summersale25'
        },
        {
          classes: 'lg:hidden',
          href: `${$addLocaleToPath('lp.gallery')}`,
          testId: 'inspiration',
          eventLabel: 'inspiration',
          eventAction: 'main',
          titleKey: 'menu.labels.inspiration'
        },
        {
          classes: 'lg:hidden',
          href: `${$addLocaleToPath('lp.tylkopro')}`,
          testId: 'tylko-pro',
          eventLabel: 'tylko-pro',
          eventAction: 'main',
          titleKey: 'menu.labels.tylko_pro'
        },
        {
          classes: 'lg:hidden',
          testId: 'redirect-showroooms',
          href: $addLocaleToPath('showrooms'),
          eventLabel: 'showrooms',
          eventAction: 'main',
          titleKey: 'Showrooms'
        }
      ]
    },
    pages: {
      testId: 'pages',
      eventAction: 'page',
      eventLabel: 'main',
      items: [
        {
          href: `${$addLocaleToPath('product-lines.index')}`,
          testId: 'product-lines',
          eventLabel: 'product-lines',
          eventAction: 'page',
          titleKey: 'menu.labels.product-lines'
        },
        {
          href: `${$addLocaleToPath('lp.samples')}`,
          testId: 'material-samples',
          eventLabel: 'material-samples',
          eventAction: 'page',
          titleKey: 'menu.labels.material-samples',
          badgeKey: isSamplePromoActive ? 'promo_label_sale' : ''
        },
        {
          href: `${$addLocaleToPath('lp.reviews')}`,
          testId: 'reviews',
          eventLabel: 'reviews',
          eventAction: 'page',
          titleKey: 'menu.labels.reviews'
        },
        {
          href: `${$addLocaleToPath('contact')}?topic=order_status`,
          testId: 'order-status',
          eventLabel: 'order-status',
          eventAction: 'page',
          titleKey: 'menu.labels.delivery_status'
        }
      ]
    },
    storage: {
      testId: 'all-storage',
      titleKey: 'common.megamenu.categories.storage',
      eventLabel: 'storage',
      eventAction: 'storage',
      badgeKey: 'plp.board.product_card.label.sale',
      href: `${$addLocaleToPath('plp')}${t('common.megamenu.categories.all_storage_url_path')}`,
      items: [
        {
          href: `${$addLocaleToPath('plp')}${t(categories.sideboard.urlPathKey)}`,
          testId: 'sideboards',
          eventLabel: 'sideboards',
          eventAction: 'storage',
          titleKey: categories.sideboard.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.bookcase.urlPathKey)}`,
          testId: 'bookcase',
          eventLabel: 'bookcase',
          eventAction: 'storage',
          titleKey: categories.bookcase.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.wallstorage.urlPathKey)}`,
          testId: 'wallstorage',
          eventLabel: 'wallstorage',
          eventAction: 'storage',
          titleKey: categories.wallstorage.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.desk.urlPathKey)}`,
          testId: 'desks',
          eventLabel: 'desks',
          eventAction: 'storage',
          titleKey: categories.desk.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.tvstand.urlPathKey)}`,
          testId: 'tv-stand',
          eventLabel: 'tv-stand',
          eventAction: 'storage',
          titleKey: categories.tvstand.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.chest.urlPathKey)}`,
          testId: 'chest-of-drawers',
          eventLabel: 'chest-of-drawers',
          eventAction: 'storage',
          titleKey: categories.chest.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.dressing_table.urlPathKey)}`,
          testId: 'dressing-table',
          eventLabel: 'dressing_table',
          eventAction: 'storage',
          titleKey: categories.dressing_table.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.shoerack.urlPathKey)}`,
          testId: 'shoe-rack',
          eventLabel: 'shoe-rack',
          eventAction: 'storage',
          titleKey: categories.shoerack.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.bedsidetable.urlPathKey)}`,
          testId: 'bedside-table',
          eventLabel: 'bedside-table',
          eventAction: 'storage',
          titleKey: categories.bedsidetable.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.vinylstorage.urlPathKey)}`,
          testId: 'vinyl-storage',
          eventLabel: 'vinyl-storage',
          eventAction: 'storage',
          titleKey: categories.vinylstorage.pluralNameKey
        },
        {
          href: `${$addLocaleToPath('plp')}${t('common.megamenu.categories.all_storage_url_path')}`,
          testId: 'all-storage-2',
          eventLabel: 'all-storage',
          eventAction: 'storage',
          titleKey: 'common.megamenu.categories.all_storage'
        }
      ]
    },
    wardrobes: {
      testId: 'wardrobes',
      titleKey: categories.wardrobe.pluralNameKey,
      eventAction: 'wardrobes',
      eventLabel: 'wardrobes',
      href: `${$addLocaleToPath('plp')}${t(categories.wardrobe.urlPathKey)}`,
      badgeKey: 'plp.board.product_card.label.sale',
      items: [
        {
          href: `${$addLocaleToPath('plp')}${t(categories.wardrobe.urlPathKey)}?types=fullyClosed`,
          testId: 'closed-wardrobes',
          eventLabel: 'closed-wardrobes',
          eventAction: 'wardrobes',
          titleKey: 'common.megamenu.categories.closed_wardrobes'
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.wardrobe.urlPathKey)}?types=partiallyOpen`,
          testId: 'semi-open-wardrobes',
          eventLabel: 'semi-open-wardrobes',
          eventAction: 'wardrobes',
          titleKey: 'common.megamenu.categories.semi_open_wardrobes'
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.wardrobe.urlPathKey)}?types=fullyOpen`,
          testId: 'open-wardrobes',
          eventLabel: 'open-wardrobes',
          eventAction: 'wardrobes',
          titleKey: 'common.megamenu.categories.open_wardrobes'
        },
        {
          href: `${$addLocaleToPath('plp')}${t(categories.wardrobe.urlPathKey)}`,
          testId: 'all-wardrobes',
          eventLabel: 'all-wardrobes',
          eventAction: 'wardrobes',
          titleKey: 'common.megamenu.categories.all_wardrobes'
        }
      ]
    },
    ...(IS_SMOOTH_AVAILABLE.value && {
      sofas: {
        testId: 'sofas',
        titleKey: 'common.megamenu.categories.smooth_furniture',
        eventAction: 'sotty',
        eventLabel: 'sotty',
        href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.sofas.urlPathKey)}/`,
        badgeKey: 'badge.storage.summersale25',
        items: [
          {
            href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.two_seater.urlPathKey)}`,
            testId: SOTTY_CATEGORIES.two_seater.name,
            eventLabel: SOTTY_CATEGORIES.two_seater.name,
            eventAction: 'sofas',
            titleKey: SOTTY_CATEGORIES.two_seater.pluralNameKey
          },
          {
            href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.three_seater.urlPathKey)}`,
            testId: SOTTY_CATEGORIES.three_seater.name,
            eventLabel: SOTTY_CATEGORIES.three_seater.name,
            eventAction: 'sofas',
            titleKey: SOTTY_CATEGORIES.three_seater.pluralNameKey
          },
          {
            href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.four_plus_seater.urlPathKey)}`,
            testId: SOTTY_CATEGORIES.four_plus_seater.name,
            eventLabel: SOTTY_CATEGORIES.four_plus_seater.name,
            eventAction: 'sofas',
            titleKey: SOTTY_CATEGORIES.four_plus_seater.pluralNameKey
          },
          {
            href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.chaise_longue.urlPathKey)}`,
            testId: SOTTY_CATEGORIES.chaise_longue.name,
            eventLabel: SOTTY_CATEGORIES.chaise_longue.name,
            eventAction: 'sofas',
            titleKey: SOTTY_CATEGORIES.chaise_longue.pluralNameKey
          },
          {
            href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.corner.urlPathKey)}`,
            testId: SOTTY_CATEGORIES.corner.name,
            eventLabel: SOTTY_CATEGORIES.corner.name,
            eventAction: 'sofas',
            titleKey: SOTTY_CATEGORIES.corner.pluralNameKey
          },
          {
            href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.armchair.urlPathKey)}`,
            testId: SOTTY_CATEGORIES.armchair.name,
            eventLabel: SOTTY_CATEGORIES.armchair.name,
            eventAction: 'sofas',
            titleKey: SOTTY_CATEGORIES.armchair.pluralNameKey
          },
          // {
          //   href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.footrests_and_modules.urlPathKey)}`,
          //   testId: SOTTY_CATEGORIES.footrests_and_modules.name,
          //   eventLabel: SOTTY_CATEGORIES.footrests_and_modules.name,
          //   eventAction: 'sofas',
          //   titleKey: SOTTY_CATEGORIES.footrests_and_modules.nameKey,
          //   badgeKey: 'common.new'
          // }
          {
            href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.cover.urlPathKey)}`,
            testId: SOTTY_CATEGORIES.cover.name,
            eventLabel: SOTTY_CATEGORIES.cover.name,
            eventAction: 'sofas',
            titleKey: t(SOTTY_CATEGORIES.cover.pluralNameKey),
            badgeKey: 'common.new'
          },
          {
            href: `${$addLocaleToPath('plp')}${t(SOTTY_CATEGORIES.sofas.urlPathKey)}/`,
            testId: 'all-sofas',
            eventLabel: SOTTY_CATEGORIES.sofas.name,
            eventAction: 'sofas',
            titleKey: 'common.megamenu.categories.smooth_all_furniture'
          }
        ]
      }
    })
  } as const;

const mobileProductsSections : Array<{
  href?: string;
  name?: string;
  key?: string;
  testId?: string;
  eventAction?: string;
  eventLabel?: string;
  titleKey?: string;
  items?: {
    testId: string;
    titleKey: string;
    href: string;
    tabName?: MagaMenuTabs;
    eventLabel: string;
    eventAction: string;
  }[]
}> = [
  menuSections.storage,
  menuSections.wardrobes,
  IS_SMOOTH_AVAILABLE.value && menuSections.sofas,
  {
    classes: 'lg-max:hidden',
    href: `${$addLocaleToPath('plp')}`,
    testId: 'all-products',
    eventLabel: 'all-products',
    eventAction: 'main',
    titleKey: 'common.category.all'
  },
  menuSections.main?.items?.[2]
].filter(Boolean);

</script>
