<template>
  <div
    class="sticky top-0 z-4 lg-max:basic-transition lg-max:transition-[top]"
    :class="{
      'pointer-events-none': isNavBarHidden,
      'lg-max:-top-[var(--header-with-ribbon-height)]': isHeaderHidingAvailable && isConfiguratorVisible
    }"
  >
    <TheHeaderRibbon
      v-if="isRibbonEnabled"
      class="z-1"
      v-on:mouseenter="toggleMegaMenu(false)"
    />
    <div class="relative">
      <div
        class="absolute top-0 w-full transform transition-transform ease-in-out duration-300"
        :class="{ '-translate-y-full': isNavBarHidden && !isMegaMenuOpened }"
      >
        <TheHeaderModernMegaMenu class="fixed top-0 w-full z-2 lg:z-0" />

        <div
          class="h-12 md:h-16 lg:h-24"
          v-on:mouseenter="toggleMegaMenu(false)"
        />

        <div class="grid-container flex items-center justify-between">
          <!-- Logo Tylko -->
          <div
            class="h-56 md:h-64 grow z-5 relative"
            v-on:mouseover="toggleMegaMenu(false)"
          >
            <Transition
              name="fade"
              v-bind="{
                enterFromClass: `opacity-0 transform ${isHomeTab ? '-translate-x-96': 'translate-x-96' }`,
                leaveToClass: `opacity-0 transform ${isHomeTab ? 'translate-x-96': '-translate-x-96' }`
              }"
              enter-active-class="transition-all duration-500 ease-out"
              leave-active-class="transition-all duration-500 ease-out"
            >
              <BaseLink
                v-if="isDesktopViewport || isHomeTab"
                variant="custom"
                data-testid="logo"
                class="absolute left-0 top-1/2 transform -translate-y-1/2"
                v-bind="{ href: $addLocaleToPath('homepage'), trackData: {} }"
              >
                <IconTylkoLogo
                  class="h-24 xl:h-32 text-orange transition-color "
                  :class="{ '!text-white' : isMegaMenuOpened }"
                />
              </BaseLink>
              <BaseButton
                v-else-if="activeMegaMenuTab === 'products'"
                variant="custom"
                data-testid="logo"
                class="flex items-center shrink-1 normal-18 text-white capitalize
                        absolute left-0 top-1/2 transform -translate-y-1/2"
                v-on:click="toggleTab('home')"
              >
                <IconCaret class="w-48 h-48 -ml-12 transform rotate-180" />
                {{ $t('menu.labels.products') }}
              </BaseButton>
            </Transition>
          </div>

          <!-- Nav bar -->
          <TheHeaderModernBar
            class="transition-[background-color] basic-transition"
            v-bind="{
              class:[
                (isThemeLight || isMegaMenuOpened ) ? '[--text-color:#1D1E1F] [--bg-color:#ffffff]' : '[--text-color:#ffffff] [--bg-color:#1D1E1F]',
                { 'bg-[#e3e3e3]/[0.01] backdrop-blur-[50px]': variant === 'transparent' && !isDarkNavigationActive },
                { 'bg-black/[0.5] backdrop-blur-[50px]': variant === 'transparent' && isDarkNavigationActive },
                { 'bg-white': variant === 'light' },
                { 'bg-neutral-900': variant === 'dark' },
                { '!backdrop-blur-none !bg-transparent': isMegaMenuOpened },
              ]
            }"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { throttledWatch, useWindowScroll, useCssVar } from '@vueuse/core';
import useHeaderModern from '~/composables/useHeaderModern';
import useMq from '~/composables/useMq';
import IconChevronRight from 'assets/icons/chevronRight.svg';

const props = withDefaults(
  defineProps<{
    variant? : 'light' | 'dark' | 'transparent';
  }>(), {
    variant: 'light'
  });

const {
  isRibbonEnabled,
  isNavBarHidden,
  isMegaMenuOpened,
  isScrolled,
  isConfiguratorScroll,
  navbarHideOffset,
  isConfiguratorVisible,
  toggleMegaMenu,
  activeMegaMenuTab,
  toggleTab,
  isHeaderHidingAvailable
} = useHeaderModern();
const { isDarkNavigationActive } = storeToRefs(useHeaderStore());

const isThemeLight = computed(() => props.variant === 'dark' || props.variant === 'transparent');

const { isDesktopViewport, isSm } = useMq();

const isHomeTab = computed(() => activeMegaMenuTab.value === 'home' || !isMegaMenuOpened.value);
const modernHeaderCurrentHeight = useCssVar('--current-header-height');
modernHeaderCurrentHeight.value = '96px';
const modernHeaderHeight = useCssVar('--header-height');
modernHeaderHeight.value = '96px';

watch(isNavBarHidden, () => {
  modernHeaderCurrentHeight.value = isNavBarHidden.value ? '0px' : modernHeaderHeight.value;
});

watch(isSm, () => {
  modernHeaderHeight.value = isSm.value ? '68px' : '96px';
}, { immediate: true });

if (import.meta.browser) {
  let scrollDirection = -1;
  let distanceFromLastScrollTop = 0;
  let scrollTopWhenDirectionWasChanged = 0;

  const { y: scrollTop } = useWindowScroll();

  watch(isMegaMenuOpened, () => {
    scrollDirection = -1;
    scrollTopWhenDirectionWasChanged = scrollTop.value;
    isNavBarHidden.value = false;
  });

  throttledWatch(scrollTop, (newScrollTop, oldScrollTop) => {
    if (newScrollTop > oldScrollTop) {
      scrollDirection !== 1 && (scrollTopWhenDirectionWasChanged = newScrollTop);
      scrollDirection = 1;
      distanceFromLastScrollTop = Math.abs(newScrollTop - scrollTopWhenDirectionWasChanged);
    } else {
      scrollDirection = -1;
    }

    if (newScrollTop > 90) {
      isScrolled.value = true;
    } else if (newScrollTop < 60) {
      isScrolled.value = false;
    }

    isNavBarHidden.value =
      !(isConfiguratorVisible.value && isConfiguratorScroll.value) &&
      isScrolled.value &&
      scrollDirection === 1 &&
      distanceFromLastScrollTop > navbarHideOffset;
  }, { throttle: 150 });
}
</script>
