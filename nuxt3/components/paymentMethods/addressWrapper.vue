<template>
  <div>
    <p
      v-if="additional && additional.length"
      :class="additionalClasses"
      v-html="additional"
    />
    <p
      :class="nameClasses"
      v-html="`${firstName} ${lastName}`"
    />
    <p
      :class="addressClasses"
      v-html="`${streetAddress1}, ${houseNumber}${streetAddress2 ? `, ${streetAddress2}` : ''}, ${postalCode} ${city}`"
    />
    <p
      :class="countryClasses"
      v-html="translatedCountry"
    />
  </div>
</template>

<script setup lang="ts">
import { regions } from '~/utils/regions';

const props = defineProps({
  firstName: {
    type: String,
    required: true
  },
  lastName: {
    type: String,
    required: true
  },
  streetAddress1: {
    type: String,
    required: true
  },
  houseNumber: {
    type: String,
    required: true
  },
  streetAddress2: {
    type: [String, Boolean],
    default: false
  },
  postalCode: {
    type: String,
    required: true
  },
  city: {
    type: String,
    required: true
  },
  country: {
    type: String,
    required: true
  },
  nameClasses: {
    type: String,
    default: ''
  },
  addressClasses: {
    type: String,
    default: ''
  },
  countryClasses: {
    type: String,
    default: ''
  },
  additionalClasses: {
    type: String,
    default: ''
  },
  additional: {
    type: String,
    default: ''
  }
});

const { $i18n } = useNuxtApp();
const translatedCountry = computed(() => $i18n.t(regions[props.country].nameKey));

</script>
