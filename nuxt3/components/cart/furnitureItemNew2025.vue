<template>
  <div class="md:flex">
    <div class="md:max-w-[300px] md:w-full md-max:px-16 md-max:pt-16">
      <BaseLink
        variant="custom"
        class="w-full flex items-start md:items-center relative"
        v-bind="{
          trackData: cartEvent('GoToConfigurator', cartItem.itemId),
          href: shelfUrl,
          'data-testid': 'cart-item-image-link'
        }"
      >
        <CartItemImage
          class="w-full aspect-square"
          v-bind="{
            cartItem
          }"
        />
        <p class="absolute md:pl-32 lg:pl-0 bottom-0 left-0 backdrop-blur-sm bg-white/50 p-4 w-full text-center normal-14">
          {{ isSampleBox ? $t('scart.delivery_time.samples') : deliveryTime }}
        </p>
      </BaseLink>
    </div>
    <div
      class="flex flex-col flex-1 pt-24 px-16 md:px-32 lg:px-16 xl:px-32 divide-y
            [&>div]:py-24 [&>ul]:py-24 divide-neutral-400"
    >
      <header class="flex justify-between gap-x-8 text-neutral-900">
        <div class="flex-1">
          <h2 class="semibold-16 mb-16 line-clamp-2">
            {{ itemName }}
          </h2>
        </div>
        <CheckoutPriceItem
          class="mb-8 max-w-[200px]"
          price-font-class="semibold-16 text-neutral-900"
          promo-price-font-class="semibold-16"
          lowest-price-font-class="normal-12"
          v-bind="{
            cartItem,
          }"
        />
      </header>
      <CartItemDescription
        class="normal-14 lg:normal-12 text-neutral-700"
        v-bind="{
          cartItem,
        }"
      >
        <FormKit
          v-if="!isSampleBox"
          v-model="quantity"
          outer-class="mt-16"
          type="stepNumber"
          label-class="normal-16 lg:normal-14"
          data-testid="quantity"
          v-bind="{
            id: quantityStepperId,
            disabled: isQuantityPending,
          }"
        >
          <template
            v-if="isQuantityPending"
            #suffixIcon
          >
            <div class="pending-spinner normal-18 ml-12" />
          </template>
        </FormKit>
        <template v-if="!isSampleBox">
          <ScartAdditionalInfo
            v-if="cartItem.has_multiple_heights"
            class="mt-16"
            :copy="$t('scart.height_message')"
          />
          <ScartAdditionalInfo
            v-if="cartItem.has_multiple_depths"
            class="mt-16"
            :copy="$t('scart.depth_message')"
          />
        </template>
      </CartItemDescription>
      <div
        style="--spinner-color: #3a3c3d;"
        class="!py-20 normal-14 mt-auto"
      >
        <div class="-mx-16 lg2:-mx-32 grid grid-cols-3 divide-x divide-neutral-400">
          <template v-if="!isSampleBox">
            <BaseLink
              variant="custom"
              class="col-start-1 w-full px-8 flex justify-center items-center"
              v-bind="{
                href: shelfUrl,
                trackData: cartEvent('EditItem', cartItem.itemId),
                'data-testid': 'cart-item-edit'
              }"
            >
              <template #icon>
                <IconCartPencil class="mr-8 text-neutral-900" />
              </template>
              {{ $t('checkout.address_summary.button.edit') }}
            </BaseLink>
            <BaseButton
              variant="custom"
              class="col-start-2 w-full px-8 flex justify-center items-center !pl-0 disabled:text-neutral-700
                      [&>.loading-spinner]:before:w-20 [&>.loading-spinner]:before:h-20 [&>.loading-spinner]:ml-0 [&>.loading-spinner]:!mr-12"

              v-bind="{
                disabled: isWishlistPending,
                pending: isWishlistPending,
                trackData: cartEvent('SaveItem', cartItem.itemId),
                'data-testid': 'cart-item-save'
              }"
              v-on:click="saveToWishlist"
            >
              <template #icon>
                <IconCartHeart class="mr-8" />
              </template>
              {{ $t('common.save') }}
            </BaseButton>
          </template>
          <BaseButton
            variant="custom"
            class="col-start-3 w-full px-8 flex justify-center items-center !pl-0
                      [&>.loading-spinner]:before:w-20 [&>.loading-spinner]:before:h-20 [&>.loading-spinner]:ml-0 [&>.loading-spinner]:!mr-12"

            v-bind="{
              disabled: isWishlistPending,
              trackData: cartEvent('RemoveItem', cartItem.itemId),
              'data-testid': 'cart-item-remove'
            }"
            v-on:click="$emit('removeFromCart')"
          >
            <template #icon>
              <IconCartTrash class="mr-8" />
            </template>
            {{ $t('common.remove') }}
          </BaseButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useToast } from 'vue-toastification';
import { useEventBus } from '@vueuse/core';
import { CART_ANALYTICS } from '~/composables/useCart';
import PDP_URL_UTILS from '~/composables/useProductUrl';
import { DECREASE_ITEM_QUANTITY, INCREASE_ITEM_QUANTITY } from '~/api/cart';
import { ADD_TO_WISHLIST_BY_ID } from '~/api/wishlist';
import type { CartItem } from '~/types/userStatus';
import { useScartStore } from '~/stores/scart';
import useSofaCartInfo from '~/composables/useSofaCartInfo';

const props = defineProps<{cartItem: CartItem}>();

const emits = defineEmits<{
  saveToWishlist: [];
  removeFromCart: [];
}>();

const quantityStepperId = useId();
const { getCartItemName, getCartItemColor, isItSampleBox } = useCart();
const { GET_SHELF_URL_INCLUDING_WEBVIEW } = PDP_URL_UTILS();

let shelfUrl = '';
let itemName = '';

const { $addLocaleToPath } = useNuxtApp();
const { cartEvent } = CART_ANALYTICS();

if (props.cartItem.content_type === 'sotty' && props.cartItem.category !== 'cover') {
  const {
    sofaItemName
  } = useSofaCartInfo(props.cartItem.material, props.cartItem.itemId);
  shelfUrl = GET_SHELF_URL_INCLUDING_WEBVIEW(
    props.cartItem.shelf_type,
    props.cartItem.physicalProductVersion,
    props.cartItem.configuratorType,
    props.cartItem.category,
    props.cartItem.shelfId,
    '',
    ''
  );
  itemName = getCartItemName(props.cartItem) || sofaItemName;
} else {
  const color = computed(() => getCartItemColor(props.cartItem));
  shelfUrl = computed(() => isSampleBox.value
    ? $addLocaleToPath('lp.samples')
    : GET_SHELF_URL_INCLUDING_WEBVIEW(
      props.cartItem.shelf_type,
      props.cartItem.physicalProductVersion,
      props.cartItem.configuratorType,
      props.cartItem.category,
      props.cartItem.shelfId,
      '',
      '',
      color.value?.cv
    ));
  itemName = computed(() => props.cartItem?.name || getCartItemName(props.cartItem));
}

const isSampleBox = computed(() => isItSampleBox(props.cartItem));

const { deliveryTime } = storeToRefs(useScartStore());
const toast = useToast();
const { $logException } = useNuxtApp();
const { t } = useI18n();
const eventBus = useEventBus<string>('cart');

const isQuantityPending = ref(false);
const isWishlistPending = ref(false);
const quantity = ref<number>(props.cartItem.quantity);
const { fetchUserStatus } = useCartStatus();

watch(quantity, async (newQuantity, oldQuantity) => {
  isQuantityPending.value = true;

  try {
    if (newQuantity > oldQuantity) {
      await INCREASE_ITEM_QUANTITY(props.cartItem.id);
      await fetchUserStatus();
      toast.success(t('scart.toast.quantity_increased'));
    } else if (newQuantity < oldQuantity) {
      await DECREASE_ITEM_QUANTITY(props.cartItem.id);
      await fetchUserStatus();
      toast.success(t('scart.toast.quantity_decreased'));
    }
  } catch (e) {
    console.error(e);
    toast.error(t('common.error.connection'));
  } finally {
    isQuantityPending.value = false;
  }
});

const { FETCH_GLOBAL } = useGlobal();
const { isSignedIn } = storeToRefs(useGlobal());

const saveToWishlist = async () => {
  if (isSignedIn.value) {
    try {
      isWishlistPending.value = true;
      await ADD_TO_WISHLIST_BY_ID(props.cartItem.itemId, props.cartItem.itemFurnitureType);
      await FETCH_GLOBAL();
      eventBus.emit('showWishlistBubble', props.cartItem);
    } catch (error) {
      $logException(error);
      toast.error(t('common.error.connection'));
    } finally {
      isWishlistPending.value = false;
    }
  } else {
    emits('saveToWishlist');
  }
};
</script>
