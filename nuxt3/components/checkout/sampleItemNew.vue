<template>
  <div class="flex lg-max:mb-16 mb-8 flex-shrink-0">
    <div class="min-w-[120px] w-[120px] mr-8">
      <BasePicture
        img-classes="object-cover w-full"
        type="A"
        v-bind="{
          path: `lp/sample/sets/${variant_type}`,
          pictureClasses: 'w-full',
          isRetinaUploaded: false
        }"
      />
    </div>
    <div class="grow flex flex-col items-start justify-between max-w-[calc(100%-128px)]">
      <div class="text-left w-full">
        <h3
          class="text-neutral-900 semibold-14"
          v-html="$t(itemDescriptionMaterial)"
        />
        <p
          class="normal-14 text-neutral-700 truncate"
          v-html="`${$t('scart.item.label.color')} ${itemDescriptionMaterial}`"
        />
      </div>
      <div
        v-if="$slots.price"
        class="mt-8 w-full"
      >
        <slot name="price" />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import useColors from '~/composables/useColors';
import { getTranslatedColor } from '~/composables/useColorName';

const props = defineProps({
  promotion: {
    type: Number,
    required: true
  },
  item_price_without_discount_regionalized_number: {
    type: Number,
    required: true
  },
  item_price_regionalized_number: {
    type: Number,
    required: true
  },
  itemDescriptionMaterial: {
    type: String,
    required: true
  },
  variant_type: {
    type: String,
    required: true
  },
  color: {
    type: String,
    required: true
  },
  shelf_type: {
    type: String,
    required: true
  }
});

const { getColor } = useColors();

const colorName = computed(() => {
  const color = getColor(props.shelf_type, props.color)?.nameKey;

  if (!color) {
    console.warn(`Color not found for shelf_type: ${props.shelf_type}, color: ${props.color}`);
    return props.color;
  }

  return getTranslatedColor(color, ' / ');
});
</script>
