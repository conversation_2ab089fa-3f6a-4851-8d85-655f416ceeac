<template>
  <figure
    class="flex flex-col h-full relative group/plp-product-card transition-all basic-transition hover:shadow-plp-product-card"
    :data-index="index"
    :class="[
      isDarkMode ? 'bg-neutral-800' : 'bg-white',
      cardWrapperAdditionalClass
    ]"
    v-on:mouseover.once="wasHovered = true"
  >
    <BaseLink
      variant="custom"
      v-bind="{
        href: currentProduct.url,
        trackData: {
          ...(Object.keys(trackData).length ? { ...trackData, eventLabel: item.category } : {}),
        },
        preventDefault: true,
      }"
      data-testid="product-card-link"
      v-on:click="handleLeftMouseButtonClick"
      v-on:click.middle="handleOtherMouseButtonsClick('middle')"
      v-on:click.right="handleOtherMouseButtonsClick('right')"
    >
      <div
        class="relative w-full overflow-hidden"
        :class="[isDoubleSizeCard ? 'pt-[calc(50%-4px)] md:pt-[calc(50%-8px)]' : 'aspect-square']"
      >
        <img
          v-bind="{
            src: currentProduct.unrealImage || currentProduct.thumbnail,
            alt: currentProduct.name,
            loading: lazyLoading ? 'lazy' : 'eager',
            fetchPriority: !lazyLoading ? 'high' : 'low'
          }"
          class="object-cover absolute top-0 left-0"
          style="min-width: calc(100% + 1px);"
          data-testid="product-card-image-instagrid"
        >
        <img
          v-if="currentProduct.unrealImage && currentProduct.thumbnail"
          v-bind="{
            src: currentProduct.thumbnail,
            alt: currentProduct.name,
            loading: lazyLoading ? 'lazy' : 'eager',
            class: [
              wasHovered ? 'block' : 'hidden',
              isProductImageLoaded && 'group-hover/plp-product-card:opacity-100 group-hover/plp-product-card:scale-100',
            ],
          }"
          loading="lazy"
          fetchpriority="low"
          style="min-width: calc(100% + 1px);"
          class="absolute inset-0 duration-500 ease-in-out
                  opacity-0 transition-all z-1
                  transform scale-[1.04]"
          data-testid="product-card-image"
          v-on:load="isProductImageLoaded = true"
        >
        <BaseBadge
          v-if="labels.length && saleLabel && extraData?.customBadges"
          variant="custom"
          class="absolute top-8 left-8 md:top-12 md:left-16 z-2 flex py-2 px-8"
          data-testid="product-card-badge"
        >
          {{ saleLabel.value }}
          <span
            v-if="extraData?.ampersand"
            class="ml-4"
            v-text="'&'"
          />
          <span
            v-if="extraData?.customLabel"
            class="ml-4"
          >
            {{ extraData?.translateLabel ? $t(extraData?.customLabel) : extraData?.customLabel }}
          </span>
        </BaseBadge>
        <BaseBadge
          v-else-if="labels.length && saleLabel"
          variant="generic"
          class="absolute top-12 left-16 z-2 flex py-2 px-8"
          data-testid="product-card-badge"
        >
          {{ saleLabel.value }}
        </BaseBadge>
        <BaseBadge
          v-if="extraData?.customBadges && item.shelfType === 10"
          variant="custom"
          class="absolute top-8 left-8 md:top-12 md:left-16 z-2 flex py-2 px-8"
          :style="{
            backgroundColor: extraData?.customBadgeTextColor,
            color: extraData?.customBadgeBackgroundColor
          }"
          data-testid="product-card-badge"
        >
          <span
            v-if="extraData?.customLabel"
            class="ml-4"
          >
            {{ extraData?.translateLabel ? $t(extraData?.customLabel) : extraData?.customLabel }}
          </span>
        </BaseBadge>

        <BaseButton
          class="configure-yours ty-btn-icon--m ty-btn-filled ty-btn-filled--dark hover:bg-white absolute bottom-8 lg:bottom-16 left-1/2 min-w-max
          -translate-x-1/2 translate-y-64 group-hover/plp-product-card:translate-y-0 md-max:translate-y-0 md-max:ty-btn-icon--s z-2"
          v-bind="{
            variant: 'icon',
            trackData: {
              eventLabel: ''
            }
          }"
        >
          <IconPencil />
          {{ $t('common.configure') }}
        </BaseButton>
      </div>
    </BaseLink>

    <aside
      v-if="isProductCardWishlistABtest && currentProduct.id"
      class="absolute z-2 top-8 right-8 w-48 h-48 bg-white rounded-full flex items-center justify-center cursor-pointer"
      v-on:click="handleWishlistClick"
    >
      <IconHeart
        ref="wishlistIcon"
        class="h-24"
        data-testid="card-heart-icon"
        :class="isWishlistActive ? 'text-orange fill-orange' : ''"
      />
    </aside>

    <div class="flex flex-col justify-between h-full md:mt-4">
      <div class="tile-group overflow-hidden h-full">
        <PlpScrollContainer
          v-if="!isDoubleSizeCard"
          :key="1"
          ref="colorSectionRef"
          class="relative flex gap-8"
          data-testid="product-card-swatches"
          v-bind="{
            emitScroll: true,
            activeElement: activeIndex,
            wrapperClasses: 'px-8 md:px-16 min-w-full'
          }"
        >
          <div
            v-for="(color, colorIndex) in sortedAvailableColors"
            :key="`material-${colorIndex}`"
            class="flex flex-col relative cursor-pointer"
            v-on:pointerdown="onPointerDown"
            v-on:pointermove="onPointerMove"
            v-on:click="() => handleSwatchClick(colorIndex)"
          >
            <img
              class="block object-cover overflow-hidden border swatch min-w-[40px] max-w-[40px] h-[50px] md:min-w-[54px] md:max-w-[54px] md:h-[68px] rounded-4"
              :class="activeIndex === colorIndex ? 'border-neutral-900' : 'border-transparent'"
              v-bind="{
                loading: lazyLoading ? 'lazy' : 'eager',
                src: color.thumbnail,
                fetchPriority: !lazyLoading ? 'high' : 'low'
              }"
            >
            <IconTick
              v-if="activeIndex === colorIndex"
              class="z-1 absolute bottom-0 right-0 w-16 h-16 bg-neutral-900 text-white rounded-tl-4 rounded-br-4"
            />
          </div>
        </PlpScrollContainer>
      </div>

      <BaseLink
        variant="custom"
        v-bind="{
          href: currentProduct.url,
          trackData: {
            ...(Object.keys(trackData).length ? { ...trackData, eventLabel: item.category } : {}),
          },
          preventDefault: true,
          'data-index': index
        }"
        class="flex flex-col flex-1 p-8 md:p-16"
        data-testid="product-card-link"
        v-on:click="handleLeftMouseButtonClick"
        v-on:click.middle="handleOtherMouseButtonsClick('middle')"
        v-on:click.right="handleOtherMouseButtonsClick('right')"
      >
        <div class="flex flex-col flex-1">
          <p
            v-if="bottomLabel && bottomLabel.translationKey"
            class="semibold-12 uppercase  mb-8 md:mb-12"
            data-testid="product-card-label"
            :class="bottomLabel?.textColorClass || ''"
            v-html="bottomLabel && bottomLabel.translationKey && $t(bottomLabel.translationKey)"
          />
          <p
            class="semibold-14"
            :class="isDarkMode ? 'text-white' : 'text-neutral-900'"
            data-testid="product-card-furniture-type"
            v-html="$t(productLineKey)"
          />
          <h2
            class="line-clamp-1 normal-14 md:mt-2"
            :class="isDarkMode ? 'text-white' : 'text-neutral-750'"
            data-testid="product-card-furniture-description"
            v-html="currentProduct.name"
          />
          <template v-if="item.category !== 'cover'">
            <h3
              v-if="isSofa"
              class="normal-14 md:mt-2"
              :class="isDarkMode ? 'text-neutral-400' : 'text-neutral-750'"
              data-testid="product-card-furniture-size"
              v-html="`${width} x ${depth} cm`"
            />
            <h3
              v-else
              class="normal-14 md:mt-2"
              :class="isDarkMode ? 'text-neutral-400' : 'text-neutral-750'"
              data-testid="product-card-furniture-size"
              v-html="`${width / 10} x ${height / 10} cm`"
            />
          </template>
        </div>

        <aside
          v-if="isPriceVisible"
          class="semibold-16 text-neutral-900 mt-8 md:mt-12"
          :class="isDarkMode ? 'text-white' : 'text-neutral-900'"
        >
          <template v-if="item.regionPrice !== item.regionPriceWithDiscount">
            <p
              v-if="GLOBAL_PROMO.percentage && !GLOBAL_PROMO.strikethroughPricing"
              class="mb-2 flex flex-wrap gap-4 items-center"
            >
              <span
                class="semibold-16 text-orange"
                data-testid="product-card-price-coupon"
              >
                {{ format((currentProduct.price * (100 - parseInt(item.discountValue))) / 100) }}
                <span
                  class="normal-16 text-neutral-900"
                  :class="isDarkMode ? 'text-white' : 'text-neutral-900'"
                >
                  {{ $t('common.with_code') }}
                </span>
              </span>

              <span
                class="px-4 py-2 semibold-12 !tracking-[0.7px] uppercase border border-neutral-900 border-dashed"
                :class="isDarkMode ? 'border-white' : 'border-neutral-900'"
              >
                {{ GLOBAL_PROMO.code }}
              </span>
            </p>
            <span
              v-else-if="GLOBAL_PROMO.percentage && GLOBAL_PROMO.strikethroughPricing"
              class="semibold-16 text-orange mr-4"
              data-testid="product-card-price-discount"
            >
              {{ format(currentProduct.priceWithDiscount) }}
            </span>
          </template>
          <span
            :class="[
              currentProduct.regionPrice === currentProduct.priceWithDiscount ? 'semibold-16' : 'normal-16 line-through',
              isDarkMode ? 'text-white' : 'text-neutral-900'
            ]"
            data-testid="product-card-price"
          >
            {{ format(currentProduct.regionPrice) }}
          </span>
        </aside>
      </BaseLink>
    </div>
  </figure>
</template>

<script setup lang="ts">
import { isNil } from 'lodash-es';
import { useMounted } from '@vueuse/core';

import { GET_PLP_PRODUCT } from '~/api/plp';
import { ADD_TO_WISHLIST_BY_ID } from '~/api/wishlist';
import { GET_PRODUCT_LINE_KEY_BY_SHELF_TYPE } from '~/utils/types';

interface Item {
  brand: string,
  category: string,
  id: number,
  price: string,
  variant: Array<any>,
  material: string | number,
  shelfType: number,
  configuratorType: number,
  physicalProductVersion: number,
  trackingList: string,
  regionPrice: number,
  regionPriceInEuro: number,
  regionPriceWithDiscount: number,
  regionPriceWithDiscountInEuro: number,
  discountValue: number,
  omnibusPrice: number,
  furnitureType: string,
  availableColors: Array<{
    id: number,
    material: number,
    thumbnail: string
  }>
}

interface swatchItem {
  id?: number,
  title?: string,
  url?: string,
  thumbnail?: string,
  regionPrice?: number,
  furnitureType?: string,
  productImageUrl?: string,
  lifestyleImageUrl?: string,
  regionPriceInEuro?: number,
  regionPriceWithDiscount?: number,
  regionPriceWithDiscountInEuro?: number,
  discountValue?: number,
}

const props = withDefaults(defineProps<{
  availableMaterials?: [],
  productUnrealImage: string,
  previewInstagrid?: string,
  productName: string,
  shelfType: SHELF_TYPE,
  labels?: Array<{ type: string, value: string }>,
  width: number,
  height: number,
  depth?: number,
  index: number,
  lazyLoading?: boolean,
  url: string,
  cardWrapperAdditionalClass?: string,
  isDarkMode?: boolean,
  isDoubleSizeCard?: boolean,
  trackData: any,
  item:Item
}>(), {
  availableMaterials: () => [],
  previewInstagrid: '',
  labels: () => [],
  lazyLoading: true,
  depth: 0,
  cardWrapperAdditionalClass: '',
  trackData: {},
  isDarkMode: false,
  isDoubleSizeCard: false
});

const { format } = usePrice();
const { GLOBAL_PROMO } = usePromoStore();
const isMounted = useMounted();
const wasHovered = ref(false);
const isProductImageLoaded = ref(false);
const { extraData, regionName, AB_TEST_VALUE, FETCH_GLOBAL } = useGlobal();
const isProductCardWishlistABtest = computed(() => isMounted.value && AB_TEST_VALUE('product_card_wishlist')?.isActive);

const $gtm = useGtm();

const isSale = false;
const isSofa = computed(() => +props.shelfType === 10);
const POINTER_THRESHOLD = 5;
const labelsProps = {
  soon: {
    textColorClass: 'text-[#8868AA]',
    translationKey: 'plp.board.product_card.label.soon'
  },
  new: {
    textColorClass: 'text-[#5A834B]',
    translationKey: 'plp.board.product_card.label.new_arrival'
  },
  discount: {
    textColorClass: isSale ? 'text-[#ffad01]' : 'text-orange',
    translationKey: isSale ? 'plp.board.product_card.label.season_sale' : 'plp.board.product_card.label.sale'
  },
  top_seller: {
    textColorClass: 'text-[#BE7958]',
    translationKey: 'plp.filters.features.top_seller'
  },
  special_edition: {
    textColorClass: 'text-neutral-800',
    translationKey: 'plp.filters.features.special_edition'
  }
} as const;

const activeIndex = ref(0);
const isWishlistActive = ref(false);
const pointerIsDragging = ref(false);
const pointerStartPosition = ref({ x: 0, y: 0 });

const currentProduct = reactive({
  url: props.url,
  id: props.item.id,
  name: props.productName,
  price: props.item.regionPrice,
  material: props.item.material,
  shelfType: props.item.shelfType,
  regionPrice: props.item.regionPrice,
  thumbnail: props.productUnrealImage,
  unrealImage: props.previewInstagrid,
  priceWithDiscount: props.item.regionPriceWithDiscount
});

watch(
  () => ({
    url: props.url,
    id: props.item.id,
    name: props.productName,
    price: props.item.regionPrice,
    material: props.item.material,
    regionPrice: props.item.regionPrice,
    thumbnail: props.productUnrealImage,
    unrealImage: props.previewInstagrid,
    priceWithDiscount: props.item.regionPriceWithDiscount
  }),
  (newValues) => {
    Object.assign(currentProduct, newValues);
  }
);

const saleLabel = computed(() => props.labels.find(label => label?.type === 'discount'));
const productLineKey = computed(() => GET_PRODUCT_LINE_KEY_BY_SHELF_TYPE(props.shelfType));

const isPriceVisible = computed(() => useFeatureFlag('PLP_PRICES') && !isNil(props.item.regionPrice));

const sortedAvailableColors = computed(() => {
  if (!props.item.availableColors || !props.item.availableColors.length) {
    return [];
  }

  const colors = [...props.item.availableColors];

  return colors.sort((a, b) => {
    if (a.material === props.item.material) { return -1; }
    if (b.material === props.item.material) { return 1; }
    return 0;
  });
});

const bottomLabel = computed(() => {
  const label = props.labels.find(
    el => el.value === 'new' ||
          el.value === 'soon' ||
          el.value === 'top_seller' ||
          el.value === 'special_edition'
  );

  return labelsProps[label?.value as keyof typeof labelsProps] || '';
});

const emit = defineEmits(['selectItemGA4Event']);

const handleLeftMouseButtonClick = async () => {
  emit('selectItemGA4Event', { item: props.item, index: props.index, eventCategory: 'left_click', currentProduct });

  await navigateTo(currentProduct.url, { external: true });
};

const handleOtherMouseButtonsClick = (buttonType: 'middle' | 'right') => {
  const isMiddleButtonClick = buttonType === 'middle';

  $gtm?.push({
    event: 'userInteraction',
    eventCategory: 'PLP',
    event_section: 'item_miniatures',
    eventAction: isMiddleButtonClick ? 'scroll_click' : 'contextmenu',
    eventLabel: 'undefined'
  });

  isMiddleButtonClick && emit('selectItemGA4Event', { item: props.item, index: props.index, eventCategory: 'scroll', currentProduct });
};

const onPointerDown = (event: MouseEvent) => {
  pointerStartPosition.value = { x: event.clientX, y: event.clientY };
  pointerIsDragging.value = false;
};

const onPointerMove = (event: MouseEvent) => {
  if (pointerIsDragging.value) { return; }

  const dx = Math.abs(event.clientX - pointerStartPosition.value.x);
  const dy = Math.abs(event.clientY - pointerStartPosition.value.y);

  if (dx > POINTER_THRESHOLD || dy > POINTER_THRESHOLD) {
    pointerIsDragging.value = true;
  }
};

const handleSwatchClick = async (colorIndex: number) => {
  if (pointerIsDragging.value) {
    return;
  }

  activeIndex.value = colorIndex;

  const id = sortedAvailableColors.value[colorIndex].id;
  const product = await GET_PLP_PRODUCT(id, props.item.furnitureType, regionName) as swatchItem;

  Object.assign(currentProduct, {
    id: product.id,
    url: product.url,
    name: product.title,
    price: product.regionPrice,
    material: product.material,
    regionPrice: product.regionPrice,
    thumbnail: product.productImageUrl,
    unrealImage: product.lifestyleImageUrl,
    priceWithDiscount: product.regionPriceWithDiscount
  });

  isWishlistActive.value = false;
};

const handleWishlistClick = async () => {
  await ADD_TO_WISHLIST_BY_ID(currentProduct.id, props.item.furnitureType as FurnitureModel);
  await FETCH_GLOBAL();

  isWishlistActive.value = true;
};
</script>
