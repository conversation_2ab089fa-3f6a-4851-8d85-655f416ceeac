<template>
  <div>
    <Head>
      <Title>{{ $t('checkout.meta.title') }}</Title>
      <Meta
        name="og:title"
        hid="og:title"
        :content="$t('checkout.meta.title')"
      />
      <Meta
        name="description"
        hid="description"
        :content="$t('checkout.meta.description')"
      />
      <Meta
        name="og:description"
        hid="og:description"
        :content="$t('checkout.meta.description')"
      />
    </Head>
    <CheckoutFrame v-if="!checkout2025">
      <template #breadcrumbs>
        <CheckoutBreadcrumbs
          v-bind="{
            eventLabel: isTypeConfirmation ? 'confirmation' : 'confirmation_pending',
            activeIndex: 3
          }"
        />
      </template>
      <template #default>
        <div>
          <div class="w-[75px] mx-auto">
            <ConfirmationSign v-if="isTypeConfirmation" />
            <PaymentPendingSign v-else />
          </div>
          <h2
            class="mt-12 mb-16 bold-20 lg:bold-24 text-offblack-800 text-center"
            v-html="
              isTypeConfirmation
                ? $t('checkout.confirmation.payment_received')
                : $t('checkout.confirmation.payment_pending_headline', { orderId })"
          />
          <p
            class="mb-24 normal-14 text-offblack-600 md:normal-16 md:mb-40 confirmation-caption"
            v-html="
              type === 'confirmation'
                ? $t('checkout.confirmation.caption', { orderId })
                : $t('checkout.confirmation.payment_pending_body')"
          />
          <p
            class="normal-16"
            v-html="$t('checkout.confirmation.delivery')"
          />
          <PaymentMethodsAddressWrapper
            v-if="formData.firstName"
            class="normal-14 text-grey-900 mt-16 mb-32"
            v-bind="{
              firstName: formData.firstName,
              lastName: formData.lastName,
              streetAddress1: formData.streetAddress1,
              houseNumber: formData.houseNumber,
              streetAddress2: formData.streetAddress2,
              postalCode: formData.postalCode,
              city: formData.city,
              country: formData.country
            }"
          />
          <div class="p-16 mb-12 bg-offwhite-600 md:mb-40">
            <h3
              class="mb-12 text-black normal-20"
              v-html="$t('checkout.confirmation.what_next')"
            />
            <div class="flex mb-12">
              <div class="mr-8 circle border-grey-800 flex-wrapper min-w-[26px]">
                <span class="normal-12 text-offblack-600">1</span>
              </div>
              <p
                class="normal-14 text-offblack-600 md:normal-16 confirmation-caption"
                v-html="$t('checkout.confirmation.email_caption', { email: formData.email })"
              />
            </div>
            <div class="flex">
              <div class="mr-8 circle border-grey-800 flex-wrapper min-w-[26px]">
                <span class="normal-12 text-offblack-600">2</span>
              </div>
              <p
                class="normal-14 text-offblack-600 md:normal-16"
                v-html="$t('checkout.confirmation.shipping_updates')"
              />
            </div>
          </div>
          <aside class="p-16 mb-12 bg-offwhite-600 md:mb-40">
            <h3
              class="mb-12 text-black normal-20"
              v-html="$t(`checkout.confirmation.${confirmationData}.title`)"
            />
            <p
              class="mb-12 normal-16 text-offblack-600"
              v-html="$t(`checkout.confirmation.${confirmationData}.subtitle`)"
            />
            <ul class="flex flex-col gap-8">
              <li
                v-for="(item, index) in infoData"
                :key="index"
                class="p-12"
                :class="item.power && 'border border-orange-900 rounded-6'"
              >
                <h4 class="flex items-center mb-4">
                  <IconCalendar
                    v-if="item.icon === 'calendar'"
                    class="w-[20px] h-[20px] mr-8"
                  />
                  <IconStopwatch
                    v-else-if="item.icon === 'stopwatch'"
                    class="w-[20px] h-[20px] mr-8"
                  />
                  <IconPlug
                    v-else-if="item.icon === 'plug'"
                    class="w-[20px] h-[20px] mr-8"
                  />
                  <span
                    class="bold-16 text-offblack-900"
                    v-html="$t(item.title)"
                  />
                </h4>
                <p
                  class="normal-16 text-offblack-600"
                  v-html="$t(item.description)"
                />
              </li>
            </ul>
          </aside>
          <div
            v-if="(formData.postcheckoutSurveyUrl && formData.postcheckoutSurveyUrl.length > 0) && (cart.shelfItemsCount > 0 || cart.hasT03)"
            class="p-16 mb-12 bg-offwhite-600 md:mb-40"
          >
            <h3
              class="mb-12 text-black normal-20"
              v-html="$t('checkout.confirmation.choosing_tylko')"
            />
            <p
              class="mb-16 normal-14 text-offblack-600 md:normal-16"
              v-html="$t('checkout.confirmation.feedback')"
            />
            <BaseButton
              variant="accent"
              v-bind="{
                trackData: {}
              }"
              v-on:click="showTypeForm = true"
              v-html="$t('checkout.confirmation.feedback_button')"
            />
            <div class="text-center">
              <ConfirmationTypeform
                v-if="showTypeForm"
                class="mt-24"
                :typeform-url="formData.postcheckoutSurveyUrl"
              />
            </div>
          </div>
        </div>
      </template>
      <template #summary>
        <ClientOnly>
          <CheckoutSummary
            :display-promo-code-remove-button="false"
            :display-assembly-remove-button="false"
            :disable-assembly-addition="true"
            :disable-promocode-addition="true"
          />
        </ClientOnly>
      </template>
    </CheckoutFrame>
    <ConfirmationNew
      v-else-if="cart.cartItems.length"
      v-bind="{
        formData,
        type
      }"
    />
    <div
      v-if="checkout2025 && (formData.postcheckoutSurveyUrl && formData.postcheckoutSurveyUrl.length > 0) && (cart.shelfItemsCount > 0 || cart.hasT03)"
      class="bg-beige-100 pt-8"
    >
      <div

        class="grid-container bg-white xl:px-[200px] py-32 lg:py-48"
      >
        <h3
          class="mb-12 text-neutral-900 semibold-20"
          v-html="$t('checkout.confirmation.choosing_tylko')"
        />
        <p
          class="mb-12 text-neutral-900 normal-16"
          v-html="$t('checkout.confirmation.feedback')"
        />
        <BaseButton
          variant="accent"
          v-bind="{
            trackData: {}
          }"
          v-on:click="showTypeForm = true"
        >
          {{ $t('checkout.confirmation.feedback_button') }}
        </BaseButton>

        <div class="text-center">
          <ConfirmationTypeform
            v-if="showTypeForm"
            class="mt-24"
            :typeform-url="formData.postcheckoutSurveyUrl"
          />
        </div>
      </div>
    </div>

    <ClientOnly>
      <ModalRecycleTax
        v-if="global.regionCode === 'FR'"
        v-bind="{
          closeEvent: cartEvent('HideEcoPartModal'),
        }"
      />
      <ModalDigiohCheckout
        v-if="showFeedbackModal"
        v-model="isDigiohModalOpen"
        v-bind="{
          orderId
        }"
      />
    </ClientOnly>
  </div>
</template>

<script setup lang="ts">
import { useToast } from 'vue-toastification';
import { storeToRefs } from 'pinia';
import useCartStatus from '~/composables/useCartStatus';
import ConfirmationSign from '~/assets/img/confirmationSignCheck.svg';
import PaymentPendingSign from '~/assets/img/paymentPendingSign.svg';
import { CART_ANALYTICS } from '~/composables/useCart';
import { checkoutAnalytics } from '~/composables/checkout/checkoutAnalytics';
import { useScartStore } from '~/stores/scart';
import { GET_FORM_DATA_CONFIRMATION } from '~/api/checkout';
import { trustPilotScript } from '~/consts/head';
import useTrustPilot from '~/composables/checkout/useTrustPilot';
const global = useGlobal();
const { $i18n, $logException } = useNuxtApp();
const checkout2025 = computed(() => global.AB_TEST_VALUE('new_checkout2025')?.isActive || global.AB_TEST_VALUE('checkout2025_100')?.isActive);

const { fetchUserStatusConfirmation } = useCartStatus();
const {
  isCartHasOnlySamples,
  sendTrustPilotInvitation
} = useTrustPilot();

const props = defineProps({
  type: {
    type: String,
    default: 'confirmation'
  }
});
const { getEcommerceEvent, getThirdPartySupplyEvent, purchaseGA4Event } = checkoutAnalytics();
const isTypeConfirmation = computed(() => props.type === 'confirmation');
const showTypeForm = ref(false);
const toast = useToast();
const gtm = useGtm();
const formData = ref({});
const { cartEvent } = CART_ANALYTICS('checkout');
const cart = useScartStore();
const { checkout2025Event } = checkoutAnalytics();
const route = useRoute();

useHead({
  link: [
    {
      rel: 'canonical',
      href: route.path
    }
  ]
});

await global.FETCH_GLOBAL();

const { orderId: storeOrderId, cartId, userId, userLanguage } = storeToRefs(useGlobal());

const orderId = route.params.orderId || storeOrderId;

const infoData = computed(() => [
  {
    title: 'checkout.confirmation.lightning.delivery.title',
    description: 'checkout.confirmation.lightning.delivery.description',
    icon: 'calendar'
  },
  {
    title: cart.cartUsedAssembly ? 'checkout.confirmation.lightning.assembly.title' : 'checkout.confirmation.self_assembly.box.title',
    description: cart.cartUsedAssembly ? 'checkout.confirmation.lightning.assembly.description' : 'checkout.confirmation.self_assembly.box.description',
    icon: 'stopwatch'
  },
  cart.hasLighting && {
    power: true,
    title: 'checkout.confirmation.lightning.power.title',
    description: 'checkout.confirmation.lightning.power.description',
    icon: 'plug'
  }
].filter(Boolean));

const confirmationData = computed(() => {
  return cart.cartUsedAssembly && cart.hasLighting ? 'lightning' : cart.cartUsedAssembly ? 'assembly' : 'self_assembly';
});

const confirmationMentionMe = computed(() => {
  return `https://tag${process.env.NODE_ENV === 'development' ? '-demo' : ''}.mention-me.com/api/v2/referreroffer/mm640fd1b0?email=${formData.value.email}&order_number=${formData.value.id}&order_total=${formData.value.totalPrice}&order_currency=${formData.value.currencyCode}&situation=postpurchase&firstname=${formData.value.firstName}&surname=${formData.value.lastName}&customer_id=${userId.value}&coupon_code=${cart.promocodeName}&locale=${userLanguage.value}`;
});

const { data, error, execute } = await GET_FORM_DATA_CONFIRMATION(orderId);
await execute();

if (error.value) {
  toast.error($i18n.t('common.error.connection'));
  $logException(error.value);

  if (error.value.statusCode === 404) {
    navigateTo('/404');
  }
} else {
  formData.value = data.value;
}

await fetchUserStatusConfirmation(orderId);

const isDigiohModalOpen = ref(false);
const showFeedbackModal = ref(false);

onMounted(() => {
  gtm.push(getThirdPartySupplyEvent());
  gtm.push(getEcommerceEvent(3, 'confirmation'));
  // This should reset compiled dataLayer, could help with mixed purchase/confirmation ecommerce keys
  gtm.push({ ecommerce: null });

  if (localStorage.getItem(`purchase-${orderId}`) !== 'ok') {
    if (checkout2025.value) {
      gtm.push(checkout2025Event('purchase', cart.chosenPaymentMethod, 'Home delivery'));
    } else {
      gtm.push(purchaseGA4Event(formData.value));
    }

    localStorage.setItem(`purchase-${orderId}`, 'ok');

    if (isTypeConfirmation.value && !isCartHasOnlySamples.value && formData.value) {
      sendTrustPilotInvitation({
        email: formData.value?.email,
        firstName: formData.value?.firstName,
        orderId
      });
    }
  }

  setTimeout(() => {
    showFeedbackModal.value = true;

    if (!localStorage.getItem('digioh-modal-shown')) {
      isDigiohModalOpen.value = true;
      localStorage.setItem('digioh-modal-shown', 'true');
    }
  }, 2000);
});

useHead({
  script: [
    { src: confirmationMentionMe.value, async: true, defer: true },
    isTypeConfirmation.value && trustPilotScript
  ].filter(Boolean)
});

</script>

<style scoped lang="scss">
.circle {
  @apply rounded-full border w-[26px] h-[26px] translate-y-[-3px] text-center;
}

:deep(.confirmation-caption) {
  span {
    @apply font-bold text-14 leading-1_4 tracking-0;

    @screen md {
      @apply font-bold text-16 leading-1_25 tracking-m-0_2;
    }
  }
}
</style>
