<template>
  <div class="bg-beige-100">
    <div class="pt-24 lg:pt-32 pb-32 grid-container lg-max:bg-white">
      <CheckoutBreadcrumbsNew
        class="!mb-[29px]"
        v-bind="{
          eventLabel: 'address',
          activeIndex: 2
        }"
      />
    </div>

    <div
      class="grid-container bg-white"
    >
      <div class="flex flex-col justify-center items-center w-full max-w-[600px] mx-auto text-center py-32 lg:py-48">
        <div class="aspect-square w-[200px] flex justify-center items-center">
          <ConfirmationSign />
        </div>
        <p class="semibold-32 text-offblack-900 mt-32">
          {{ t('checkout.confirmation.thank.you_subheadline', { name: `${formData.firstName}` }) }}
        </p>
        <p class="mt-8 semibold-16 text-offblack-900">
          {{ t('checkout.confirmations.email_body', { email_address: formData.email }) }}
        </p>
        <div class="flex w-full mt-32 lg-max:flex lg-max:flex-col">
          <BaseLink
            variant="accent"
            class="w-full mb-16 [p+&]:mt-32"
            v-bind="{
              href: $addLocaleToPath('plp'),
              trackData: { eventLabel: 'cart-empty-login' },
              'data-testid': 'cart-empty-login'
            }"
          >
            {{ t('library.continue_shopping_button') }}
          </BaseLink>
          <BaseLink
            variant="outlined"
            class="w-full lg:ml-8 mb-16 [p+&]:mt-32"
            v-bind="{
              href: `${$addLocaleToPath('contact')}?topic=order_status&order_id=${formData.orderId}&postal_code=${formData.postalCode}`,
              trackData: { eventLabel: 'cart-empty-shop' },
              'data-testid': 'cart-empty-shop'
            }"
          >
            {{ t('checkout.confiramtion.order.details_button') }}
          </BaseLink>
        </div>
      </div>
    </div>
    <div class="grid-container bg-white xl:px-[200px] mt-8 py-32 lg:py-48">
      <div
        class="grid grid-cols-1 lg:gap-32"
        :class="{
          'lg:grid-cols-4': icons && copy,
          'lg:grid-cols-3': !icons
        }"
      >
        <div class="lg-max:mb-24">
          <h3 class="text-neutral-900 semibold-20 pb-8">
            {{ t('checkout.confiramtion.order.details_button') }}
          </h3>
          <p class="text-neutral-900 semibold-16 mb-2">
            {{ t('checkout.confirmation.payment.status_subheadline') }}:
            <span
              class="px-12 py-2 rounded-12 semibold-16 ml-8"
              :class="{
                'text-success-500 bg-success-100 ': type === 'confirmation',
                'text-alert-500 bg-alert-100 ': type !== 'confirmation'
              }"
            >
              {{ type === 'confirmation' ? t('checkout.summary.status.paid') : t('checkout.confirmation.payment.status_waiting') }}
            </span>
          </p>
          <p class="text-neutral-900 semibold-16 flex justify-start items-center mb-2">
            <span>{{ t('checkout.summary.order_number') }}: {{ formData.orderId }}</span><span
              class="inline cursor-pointer ml-8"
              v-on:click="handleCopy"
            ><IconCopy class="w-24 h-24" /></span>
          </p>
          <p class="text-neutral-900 semibold-16 mb-2">
            {{ t('delivery_time_frames.choose_dates_modal.date_no', { index: placedAt }) }}
          </p>
        </div>
        <div class="lg-max:mb-24">
          <h3 class="text-neutral-900 semibold-20 pb-8">
            {{ t('checkout.summary.headline') }}
          </h3>
          <PaymentMethodsAddressWrapper
            v-if="formData.firstName"
            class="mb-12"
            v-bind="{
              nameClasses: 'semibold-16 text-neutral-900',
              addressClasses: 'normal-14 text-neutral-750',
              countryClasses: 'normal-14 text-neutral-750',
              firstName: formData.firstName,
              lastName: formData.lastName,
              streetAddress1: formData.streetAddress1,
              houseNumber: formData.houseNumber,
              streetAddress2: formData.streetAddress2,
              postalCode: formData.postalCode,
              city: formData.city,
              country: formData.country
            }"
          />
          <h4
            class="semibold-16 text-neutral-900 mb-8"
          >
            {{ t('checkout.confirmation.delivery_new.instructions_subheadline') }}
          </h4>
          <p class="normal-14 text-neutral-750 mb-12">
            {{ formData.notes ? formData.notes : '-' }}
          </p>
          <h4
            class="semibold-16 text-neutral-900 mb-8"
          >
            {{ t('account.billing_address') }}
          </h4>
          <PaymentMethodsAddressWrapper
            v-if="formData.invoiceFirstName"
            v-bind="{
              nameClasses: 'semibold-16 text-neutral-900',
              additionalClasses: 'normal-14 text-neutral-750',
              addressClasses: 'normal-14 text-neutral-750',
              countryClasses: 'normal-14 text-neutral-750',
              additional:formData.invoiceCompanyName,
              firstName: formData.invoiceFirstName,
              lastName: formData.invoiceLastName,
              streetAddress1: formData.invoiceStreetAddress1,
              postalCode: formData.invoiceStreetAddress1,
              city: formData.invoiceCity,
              country: formData.invoiceCountry
            }"
          />
          <p
            v-else
            class="normal-14 text-neutral-750"
          >
            {{ t('checkout.billing.same_as_billing.label') }}
          </p>
        </div>
        <div class="lg-max:mb-24">
          <h3 class="text-neutral-900 semibold-20 pb-8">
            {{ t('footer.contact_details') }}
          </h3>
          <p class="normal-14 text-neutral-750">
            {{ formData.email }}
          </p>
          <p class="normal-14 text-neutral-750">
            {{ formData.phone }}
          </p>
        </div>
        <div class="lg-max:mb-24">
          <h3 class="text-neutral-900 semibold-20 pb-8">
            {{ t('checkout.confirmation.payment.method_subheadline') }}
          </h3>
          <div
            v-if="icons && copy"
            class="flex items-center justify-start"
          >
            <div class="block">
              <component
                :is="paymentIcons[chosenPaymentMethod as keyof typeof paymentIcons]"
                class="w-48 h-24 lg:w-64 lg:h-32 border border-white rounded-4"
              />
            </div>

            <p class="semibold-16 text-neutral-900 ml-[7px]">
              {{ t(copy) }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <div class="grid-container bg-white xl:px-[200px] mt-8 py-32 lg:py-48">
      <h3 class="text-neutral-900 semibold-20 mb-24">
        {{ t('cart.order_summary') }}
      </h3>
      <CheckoutItemsListNew
        v-bind="{
          cartItems,
          displayReminder: true,
          displayCartLink: false
        }"
      />
      <CheckoutSummaryDetailsNew
        v-bind="{
          displayAssemblyPrice: true,
          displayNewsletter: false
        }"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import { useScartStore } from '~/stores/scart';
import { regionPaymentMethods, paymentMethodsAssets } from '~/utils/regionPaymentMethods';

import ConfirmationSign from '~/assets/img/confirmationSignCheck.svg';
import CreditCard from '~/assets/icons/payment/color/creditCard.svg';
import Paypal from '~/assets/icons/payment/color/paypal.svg';
import Klarna from '~/assets/icons/payment/color/klarna.svg';
import Eps from '~/assets/icons/payment/color/eps.svg';
import BankTransfer from '~/assets/icons/payment/color/bankTransfer.svg';
import Billie from '~/assets/icons/payment/color/billie.svg';
import Gpay from '~/assets/icons/payment/color/gpay.svg';
import BancontactCard from '~/assets/icons/payment/color/bancontactCard.svg';
import PayconiqByBancontact from '~/assets/icons/payment/color/payconiqByBancontact.svg';
import Finnishebanking from '~/assets/icons/payment/color/finnishebanking.svg';
import Ideal from '~/assets/icons/payment/color/ideal.svg';
import Przelwy24 from '~/assets/icons/payment/color/przelwy24.svg';
import Twint from '~/assets/icons/payment/color/twint.svg';
import Applepay from '~/assets/icons/payment/color/applepay.svg';
import Blik from '~/assets/icons/payment/color/blik.svg';
import PayLaterWithWalley from '~/assets/icons/payment/color/payLaterWithWalley.svg';
import Trustly from '~/assets/icons/payment/color/trustly.svg';
import Vipps from '~/assets/icons/payment/color/vipps.svg';
import Multibanco from '~/assets/icons/payment/color/multibanco.svg';

const props = defineProps({
  formData: {
    type: Object,
    required: true
  },
  type: {
    type: String,
    default: 'confirmation'
  }
});

const { t } = useNuxtApp().$i18n;
const { regionName } = storeToRefs(useGlobal());
const { chosenPaymentMethod, placedAt, cartItems } = storeToRefs(useScartStore());

const icons = regionPaymentMethods[regionName.value];

const paymentIcons = {
  scheme: CreditCard,
  paypal: Paypal,
  klarna: Klarna,
  eps: Eps,
  bankTransfer_IBAN: BankTransfer,
  klarna_b2b: Billie,
  paywithgoogle: Gpay,
  bcmc: BancontactCard,
  bcmc_mobile: PayconiqByBancontact,
  ebanking_FI: Finnishebanking,
  ideal: Ideal,
  onlineBanking_PL: Przelwy24,
  twint: Twint,
  applepay: Applepay,
  blik: Blik,
  payLaterWithWalley: PayLaterWithWalley,
  trustly: Trustly,
  vipps: Vipps,
  multibanco: Multibanco
};

const copy = computed(() => {
  return paymentMethodsAssets[chosenPaymentMethod.value as keyof typeof paymentMethodsAssets]?.copy;
});

const handleCopy = () => {
  navigator.clipboard.writeText(props.formData.orderId);
};
</script>
