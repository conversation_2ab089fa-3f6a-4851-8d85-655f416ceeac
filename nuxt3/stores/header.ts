import { isDefined } from '@vueuse/core';
import useMq from '~/composables/useMq';

export type MagaMenuTabs = 'home' | 'products';

export const useHeaderStore = defineStore('HeaderStore', () => {
  const { $dixa } = useNuxtApp();
  const isMegaMenuOpened = ref(false);
  const isFooterVisible = ref(false);
  const isPageScrolled = ref(false);
  const isNavBarHidden = ref(false);
  const isScrolled = ref(false);
  const isDarkNavigationActive = ref(true);
  const activeMegaMenuTab = ref<MagaMenuTabs>('home');

  const { isMobileOrTabletViewport } = useMq();
  const { ribbon: ribbonData } = storeToRefs(useGlobal());
  const isRibbonEnabled = computed(() => ribbonData.value?.enabled);
  const ribbonHeight = computed(() => isRibbonEnabled.value ? 52 : 0);

  const toggleDixaWidget = () => {
    $dixa.setWidgetVisibility(!isMegaMenuOpened.value);
  };

  const toggleMenu = (value?:boolean) => {
    if (value === isMegaMenuOpened.value) { return; }
    isScrolled.value = false;
    activeMegaMenuTab.value = 'home';

    if (isDefined(value)) {
      isMegaMenuOpened.value = value;
    } else {
      isMegaMenuOpened.value = !isMegaMenuOpened.value;
    }

    toggleDixaWidget();
  };

  const toggleMegaMenu = (value?:boolean) => {
    if (isMobileOrTabletViewport.value) { return; }
    toggleMenu(value);
  };

  const setIsDarkNavigation = (value?:boolean) => {
    isDarkNavigationActive.value = value ?? !isDarkNavigationActive.value;
  };

  return {
    setIsDarkNavigation,
    isDarkNavigationActive,
    toggleMegaMenu,
    toggleMobileMegaMenu: toggleMenu,
    isMegaMenuOpened,
    isPageScrolled,
    isNavBarHidden,
    isScrolled,
    isFooterVisible,
    activeMegaMenuTab,
    ribbonHeight
  };
});
