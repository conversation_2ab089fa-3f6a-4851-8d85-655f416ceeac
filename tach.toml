exact = true
exclude = [
    "**/*__pycache__",
    "**/*egg-info",
    "**/docs",
    "**/tests",
    "**/venv",
]
source_roots = [ "src",
]

[[interfaces]]
expose = ["*"]
from = ["production_margins"]
visibility = ["producers", "gallery", "kpi", "material_recovery", "model_transfers", "warehouse"]

[[interfaces]]
expose = ["published_api.*"]
from = ["production_margins"]

[[modules ]]
path = "manage"
depends_on = []

[[modules ]]
path = "custom"
depends_on = ["regions", "events", "orders", "user_profile", "vouchers", "carts", "mailing", "rating_tool", "promotions", "cstm_be", "checkout", "kpi", "admin_customization", "product_feeds", "pricing_v3", "producers", "abtests", "logger", "gallery", "django_user_agents"]

[[modules ]]
path = "taskapp"
depends_on = ["feeds", "events", "custom_audiences", "invoice", "promotions", "admin_customization", "carts", "render_tasks", "django_mailer", "producers", "complaints", "automatic_batching", "customer_service", "custom", "dixa", "gallery", "reviews", "product_feeds", "dynamic_delivery", "pricing_v3", "mailing", "free_returns", "warehouse", "kpi", "orders"]

[[modules ]]
path = "dixa"
depends_on = ["cstm_be", "complaints", "orders", "custom", "producers", "user_profile"]

[[modules ]]
path = "events"
depends_on = ["orders", "mailing", "user_profile", "invoice", "custom"]

[[modules ]]
path = "gallery"
depends_on = ["product_feeds", "feeds", "warehouse", "admin_customization", "producers", "events", "production_margins", "complaints", "ecommerce_api", "reviews", "user_profile", "vouchers", "render_tasks", "abtests", "services", "carts", "promotions", "mailing", "catalogue", "dynamic_delivery", "orders", "pricing_v3", "regions", "custom", "waiting_list"]

[[modules ]]
path = "json_migration"
depends_on = []

[[modules ]]
path = "accounting"
depends_on = ["orders", "logger", "complaints", "regions", "invoice", "custom"]

[[modules ]]
path = "django_mailer"
depends_on = ["mailing"]

[[modules ]]
path = "db_separation"
depends_on = []

[[modules ]]
path = "customer_service"
depends_on = ["payments", "carts", "producers", "invoice", "kpi", "pricing_v3", "orders", "django_mailer", "free_returns", "custom", "complaints", "gallery", "user_profile", "accounting", "logger", "regions", "vouchers", "mailing", "events"]

[[modules ]]
path = "logs"
depends_on = []

[[modules ]]
path = "invoice"
depends_on = ["regions", "cstm_be", "custom", "orders", "catalogue", "mailing", "payments", "complaints", "logger", "producers", "customer_service", "events", "services", "gallery"]

[[modules ]]
path = "carts"
depends_on = ["user_profile", "custom", "ecommerce_api", "events", "abtests", "pricing_v3", "regions", "gallery", "services", "dynamic_delivery", "orders", "promotions", "vouchers"]

[[modules ]]
path = "logger"
depends_on = []

[[modules ]]
path = "cstm_be"
depends_on = ["payments", "custom", "gallery", "dixa"]

[[modules ]]
path = "catalogue"
depends_on = ["django_user_agents", "gallery", "vouchers", "render_tasks", "promotions", "orders", "feeds", "producers", "custom", "regions", "ecommerce_api"]

[[modules ]]
path = "b2b"
depends_on = ["carts", "orders", "vouchers", "custom", "gallery"]

[[modules ]]
path = "reviews"
depends_on = ["orders", "ecommerce_api", "gallery", "custom", "feeds"]

[[modules ]]
path = "admin_customization"
depends_on = ["reviews", "producers", "shortener", "kpi", "taskapp", "gallery", "payments", "invoice", "orders", "customer_service", "cstm_be", "custom", "mailing", "regions", "user_profile"]

[[modules ]]
path = "templates"
depends_on = []

[[modules ]]
path = "promotions"
depends_on = ["custom", "regions", "gallery", "frontend_cms", "ecommerce_api", "vouchers", "pricing_v3"]

[[modules ]]
path = "feeds"
depends_on = ["gallery", "regions", "custom", "render_tasks", "product_feeds", "catalogue", "promotions", "taskapp"]

[[modules ]]
path = "custom_audiences"
depends_on = ["reviews", "gallery", "cstm_be", "orders", "complaints", "custom"]

[[modules ]]
path = "dynamic_delivery"
depends_on = ["custom", "orders", "abtests", "producers", "gallery"]

[[modules ]]
path = "gallery_editor"
depends_on = ["gallery", "producers"]

[[modules ]]
path = "complaints"
depends_on = ["cstm_be", "custom", "loose_ends", "gallery", "mailing", "orders", "customer_service", "events", "dixa", "invoice", "free_returns", "producers", "user_profile", "production_margins"]

[[modules ]]
path = "render_tasks"
depends_on = ["gallery", "catalogue", "custom", "feeds"]

[[modules ]]
path = "internal_api"
depends_on = []

[[modules ]]
path = "ecommerce_api"
depends_on = ["mailing", "promotions", "carts", "custom", "abtests", "user_profile", "regions", "frontend_cms", "orders", "gallery", "reviews", "waiting_list"]

[[modules ]]
path = "vouchers"
depends_on = ["promotions", "frontend_cms", "mailing", "pricing_v3", "carts", "orders", "ecommerce_api", "regions", "custom", "gallery"]

[[modules ]]
path = "media"
depends_on = []

[[modules ]]
path = "abtests"
depends_on = ["custom", "django_user_agents", "regions"]

[[modules ]]
path = "mailing"
depends_on = ["waiting_list", "custom", "kpi", "vouchers", "events", "complaints", "orders", "regions", "user_profile", "warehouse", "gallery", "admin_customization"]

[[modules ]]
path = "product_feeds"
depends_on = ["gallery", "admin_customization", "custom", "regions", "promotions"]

[[modules ]]
path = "showrooms"
depends_on = ["regions", "custom"]

[[modules ]]
path = "pricing_v3"
depends_on = ["regions", "orders", "producers", "vouchers", "promotions", "gallery", "carts", "ecommerce_api", "custom", "services"]

[[modules ]]
path = "rest_auth"
depends_on = ["vouchers", "regions", "custom", "mailing", "events", "user_profile", "orders", "carts"]

[[modules ]]
path = "waiting_list"
depends_on = ["mailing", "carts", "gallery", "regions", "custom", "cstm_be"]

[[modules ]]
path = "locale"
depends_on = []

[[modules ]]
path = "fixtures"
depends_on = []

[[modules ]]
path = "user_consents"
depends_on = []

[[modules ]]
path = "warehouse"
depends_on = ["gallery", "pricing_v3", "cstm_be", "orders", "mailing", "custom", "loose_ends", "regions"]

[[modules ]]
path = "producers"
depends_on = ["gallery", "cstm_be", "reviews", "complaints", "admin_customization", "orders", "dynamic_delivery", "logger", "kpi", "custom", "production_margins", "automatic_batching", "mailing", "events"]

[[modules ]]
path = "free_returns"
depends_on = ["customer_service", "producers", "events", "complaints", "orders", "invoice", "custom", "loose_ends"]

[[modules ]]
path = "production_margins"
depends_on = ["orders", "kpi", "mailing", "producers", "cstm_be", "admin_customization", "custom", "gallery"]

[[modules ]]
path = "orders"
depends_on = ["warehouse", "customer_service", "invoice", "gallery", "waiting_list", "events", "checkout", "logger", "mailing", "regions", "free_returns", "pricing_v3", "vouchers", "dixa", "user_profile", "kpi", "abtests", "promotions", "custom", "complaints", "payments", "producers", "carts", "services"]

[[modules ]]
path = "payments"
depends_on = ["custom", "b2b", "invoice", "pricing_v3", "checkout", "ecommerce_api", "orders", "regions", "mailing", "producers", "user_profile", "events", "carts"]

[[modules ]]
path = "loose_ends"
depends_on = ["frontend_cms", "regions", "custom"]

[[modules ]]
path = "automatic_batching"
depends_on = ["custom", "complaints", "producers"]

[[modules ]]
path = "checkout"
depends_on = ["user_profile", "events", "pricing_v3", "payments", "custom", "carts", "orders", "cstm_be", "frontend_cms", "regions", "invoice"]

[[modules ]]
path = "shortener"
depends_on = ["custom"]

[[modules ]]
path = "user_profile"
depends_on = ["regions", "carts", "orders", "abtests", "complaints", "invoice", "mailing", "customer_service", "django_mailer", "ecommerce_api", "vouchers", "checkout", "gallery", "events", "custom", "dixa", "reviews"]

[[modules ]]
path = "material_recovery"
depends_on = ["user_profile", "gallery", "admin_customization", "cstm_be", "producers", "custom", "production_margins", "mailing"]

[[modules ]]
path = "django_user_agents"
depends_on = ["abtests", "regions"]

[[modules ]]
path = "kpi"
depends_on = ["producers", "vouchers", "free_returns", "orders", "regions", "production_margins", "payments", "complaints", "loose_ends", "custom", "user_profile", "gallery", "admin_customization"]

[[modules ]]
path = "items_for_render"
depends_on = ["custom"]

[[modules ]]
path = "rating_tool"
depends_on = ["custom", "promotions", "regions", "gallery"]

[[modules ]]
path = "conftest"
depends_on = ["carts", "gallery", "regions", "custom", "invoice", "user_profile", "vouchers"]

[[modules ]]
path = "model_transfers"
depends_on = ["abtests", "gallery", "user_profile", "regions", "showrooms", "complaints", "invoice", "pricing_v3", "catalogue", "reviews", "warehouse", "orders", "custom", "producers", "user_consents", "production_margins", "dynamic_delivery", "automatic_batching"]

[[modules ]]
path = "frontend_cms"
depends_on = ["regions", "orders", "django_user_agents", "gallery", "custom", "user_profile", "cstm_be"]

[[modules ]]
path = "services"
depends_on = ["gallery", "carts", "regions", "custom"]

[[modules ]]
path = "regions"
depends_on = ["payments", "pricing_v3", "frontend_cms", "carts", "orders", "events", "gallery", "custom", "ecommerce_api", "mailing", "abtests"]
